#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final deep clean of all old admin/owner traces from VL2024.sqlite
"""

import sqlite3
import os
import sys
from datetime import datetime
from config import ADMIN_IDS

def final_deep_clean():
    """Perform final deep clean of all old admin traces"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🧹 Performing final deep clean of VL2024.sqlite...")
        
        # Old admin IDs and usernames to clean
        old_admin_ids = [5702253746, 6404812540, 7440280333, 8046165511]
        old_usernames = [
            'thekingmuslim', 'eziz_official', 'a_nazarow77', 'aylar_kaa', 
            'nerwa_degme', 'baglan', 'old_user_'
        ]
        
        print(f"Current admins (to preserve): {ADMIN_IDS}")
        print(f"Old admin IDs to clean: {old_admin_ids}")
        print(f"Old usernames to clean: {old_usernames}")
        
        # Get all tables except phone data
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        all_tables = cursor.fetchall()
        
        tables_to_clean = [t[0] for t in all_tables if not t[0].startswith('Tel')]
        
        print(f"\n📊 Tables to clean: {tables_to_clean}")
        
        total_cleaned = 0
        
        for table_name in tables_to_clean:
            print(f"\n🗂️ Deep cleaning table: {table_name}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # Clean by user_id
            if 'user_id' in column_names:
                for old_id in old_admin_ids:
                    if table_name == 'users':
                        # For users table, just ensure they're reset (don't delete)
                        cursor.execute(f"""
                            UPDATE {table_name} 
                            SET search_points = 1,
                                is_vip = 0,
                                vip_expiry_date = NULL
                            WHERE user_id = ? AND user_id NOT IN ({','.join(['?' for _ in ADMIN_IDS])})
                        """, [old_id] + ADMIN_IDS)
                        if cursor.rowcount > 0:
                            print(f"   ✅ Reset user {old_id} in {table_name}")
                            total_cleaned += cursor.rowcount
                    else:
                        # For other tables, we can be more aggressive
                        # But let's keep the data for history, just mark it
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE user_id = ?", (old_id,))
                        count = cursor.fetchone()[0]
                        if count > 0:
                            print(f"   ℹ️ Found {count} records for old admin {old_id} in {table_name} (keeping for history)")
            
            # Clean username references
            username_cols = [col for col in column_names if 'username' in col.lower() or 'name' in col.lower()]
            for col in username_cols:
                for old_username in old_usernames:
                    try:
                        # Check if there are records to update
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE LOWER({col}) LIKE ?", (f'%{old_username.lower()}%',))
                        count = cursor.fetchone()[0]
                        
                        if count > 0:
                            # Update username to mark as old
                            cursor.execute(f"""
                                UPDATE {table_name} 
                                SET {col} = 'old_user_' || {col}
                                WHERE LOWER({col}) LIKE ? AND {col} NOT LIKE 'old_user_%'
                            """, (f'%{old_username.lower()}%',))
                            
                            updated = cursor.rowcount
                            if updated > 0:
                                print(f"   ✅ Updated {updated} username references for '{old_username}' in {col}")
                                total_cleaned += updated
                    except sqlite3.Error as e:
                        print(f"   ❌ Error updating {col}: {e}")
            
            # Clean any 'created_by' or 'referrer_id' references
            creator_cols = [col for col in column_names if 'created_by' in col.lower() or 'referrer' in col.lower()]
            for col in creator_cols:
                for old_id in old_admin_ids:
                    try:
                        # Reassign to current owner instead of deleting
                        current_owner = ADMIN_IDS[0]  # First admin (owner)
                        cursor.execute(f"""
                            UPDATE {table_name} 
                            SET {col} = ?
                            WHERE {col} = ?
                        """, (current_owner, old_id))
                        
                        if cursor.rowcount > 0:
                            print(f"   ✅ Reassigned {cursor.rowcount} {col} references from {old_id} to {current_owner}")
                            total_cleaned += cursor.rowcount
                    except sqlite3.Error as e:
                        print(f"   ❌ Error updating {col}: {e}")
        
        # Final verification - ensure current admins have proper privileges
        print(f"\n👑 Final verification of current admin privileges...")
        for admin_id in ADMIN_IDS:
            cursor.execute("""
                UPDATE users 
                SET search_points = 999999999,
                    is_vip = 1,
                    vip_expiry_date = '2099-12-31'
                WHERE user_id = ?
            """, (admin_id,))
            
            if cursor.rowcount > 0:
                print(f"   ✅ Confirmed unlimited privileges for admin {admin_id}")
        
        # Commit all changes
        conn.commit()
        
        print(f"\n🎉 Final deep clean completed!")
        print(f"📊 Summary:")
        print(f"   - Total records cleaned/updated: {total_cleaned}")
        print(f"   - Old admin privileges removed")
        print(f"   - Current admin privileges confirmed")
        print(f"   - Username references marked as old")
        print(f"   - Creator/referrer references reassigned")
        print(f"📅 Cleaned at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Final status report
        print(f"\n📋 Final Status Report:")
        
        # Check current admin status
        for admin_id in ADMIN_IDS:
            cursor.execute("SELECT username, search_points, is_vip FROM users WHERE user_id = ?", (admin_id,))
            result = cursor.fetchone()
            if result:
                username, points, is_vip = result
                vip_status = "✅ VIP" if is_vip else "❌ Not VIP"
                print(f"   👑 Admin {admin_id} (@{username}): {points} points, {vip_status}")
            else:
                print(f"   ⚠️ Admin {admin_id}: Not found in database")
        
        # Check old admin status
        print(f"\n   Old admin status:")
        for old_id in old_admin_ids:
            cursor.execute("SELECT username, search_points, is_vip FROM users WHERE user_id = ?", (old_id,))
            result = cursor.fetchone()
            if result:
                username, points, is_vip = result
                vip_status = "❌ VIP Removed" if not is_vip else "⚠️ Still VIP"
                print(f"   👤 Old admin {old_id} (@{username}): {points} points, {vip_status}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Final Deep Clean")
    print("=" * 60)
    
    response = input("⚠️ Perform final deep clean of all old admin traces? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        final_deep_clean()
    else:
        print("❌ Deep clean cancelled")
    
    print("\n✅ Final deep clean complete!")

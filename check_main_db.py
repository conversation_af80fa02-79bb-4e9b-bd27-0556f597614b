#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Scrip<PERSON> to check and fix user points in main database (VL2024.sqlite)
"""

import sqlite3
import os
import sys
from config import ADMIN_IDS

def check_main_database():
    """Check main database for users"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking main database (VL2024.sqlite)...")
        
        # Check users table structure
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Users table columns: {column_names}")
        
        # Get user count
        cursor.execute("SELECT COUNT(*) FROM users")
        user_count = cursor.fetchone()[0]
        print(f"Total users: {user_count}")
        
        if user_count > 0:
            # Show users with high points
            points_columns = [col for col in column_names if 'point' in col.lower() or 'balance' in col.lower() or 'search' in col.lower()]
            print(f"Potential points columns: {points_columns}")
            
            # Try to find users with high values
            for col in points_columns:
                try:
                    cursor.execute(f"SELECT user_id, username, {col} FROM users WHERE {col} > 100 ORDER BY {col} DESC LIMIT 10")
                    high_users = cursor.fetchall()
                    if high_users:
                        print(f"\nUsers with high {col}:")
                        for user_id, username, value in high_users:
                            admin_status = "👑 ADMIN" if user_id in ADMIN_IDS else "👤 USER"
                            print(f"  {admin_status} {user_id} (@{username or 'unknown'}): {value}")
                except sqlite3.Error:
                    continue
            
            # Show all users (first 20)
            cursor.execute("SELECT user_id, username FROM users LIMIT 20")
            all_users = cursor.fetchall()
            print(f"\nFirst 20 users:")
            for user_id, username in all_users:
                admin_status = "👑 ADMIN" if user_id in ADMIN_IDS else "👤 USER"
                print(f"  {admin_status} {user_id} (@{username or 'unknown'})")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def fix_main_database_points():
    """Fix user points in main database"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔧 Fixing user points in main database...")
        
        # Get table structure
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        # Find points-related columns
        points_columns = []
        for col in column_names:
            col_lower = col.lower()
            if any(keyword in col_lower for keyword in ['point', 'balance', 'search', 'credit']):
                points_columns.append(col)
        
        print(f"Found potential points columns: {points_columns}")
        
        if not points_columns:
            print("❌ No points columns found")
            return
        
        # Get all users
        cursor.execute("SELECT user_id, username FROM users")
        users = cursor.fetchall()
        
        reset_count = 0
        admin_count = 0
        
        for user_id, username in users:
            if user_id in ADMIN_IDS:
                # Set unlimited points for admins
                for col in points_columns:
                    try:
                        cursor.execute(f"UPDATE users SET {col} = 999999999 WHERE user_id = ?", (user_id,))
                    except sqlite3.Error:
                        continue
                print(f"✅ Admin unlimited points: {user_id} (@{username or 'unknown'})")
                admin_count += 1
            else:
                # Set 1 point for regular users
                for col in points_columns:
                    try:
                        cursor.execute(f"UPDATE users SET {col} = 1 WHERE user_id = ?", (user_id,))
                    except sqlite3.Error:
                        continue
                print(f"🔄 Reset to 1 point: {user_id} (@{username or 'unknown'})")
                reset_count += 1
        
        conn.commit()
        
        print(f"\n🎉 Main database points fixed!")
        print(f"📊 Summary:")
        print(f"   - Users reset to 1 point: {reset_count}")
        print(f"   - Admins with unlimited points: {admin_count}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Main Database Check")
    print("=" * 50)
    
    check_main_database()
    
    response = input("\n⚠️ Fix user points in main database? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        fix_main_database_points()
    
    print("\n✅ Done!")

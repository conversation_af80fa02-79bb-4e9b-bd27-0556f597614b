#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final status check after all cleaning operations
"""

import sqlite3
import os
from config import ADMIN_IDS, OWNER_ID, BOT_USERNAME

def final_status_check():
    """Final status check"""
    
    print("🔍 FINAL STATUS CHECK")
    print("=" * 60)
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} not found.")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Bot Configuration
        print(f"1️⃣ Bot Configuration:")
        print(f"   ✅ Owner ID: {OWNER_ID}")
        print(f"   ✅ Bot Username: {BOT_USERNAME}")
        print(f"   ✅ Admin IDs: {ADMIN_IDS}")
        
        # 2. Phone Database Status
        print(f"\n2️⃣ Phone Database Status:")
        cursor.execute("SELECT COUNT(*) FROM Tel1")
        phone_count = cursor.fetchone()[0]
        print(f"   ✅ Phone records: {phone_count:,} (preserved)")
        
        # 3. User System Status
        print(f"\n3️⃣ User System Status:")
        cursor.execute("SELECT COUNT(*) FROM users")
        total_users = cursor.fetchone()[0]
        print(f"   ✅ Total users: {total_users}")
        
        # 4. Admin Status
        print(f"\n4️⃣ Current Admin Status:")
        all_admins_ok = True
        for admin_id in ADMIN_IDS:
            cursor.execute("SELECT username, search_points, is_vip FROM users WHERE user_id = ?", (admin_id,))
            result = cursor.fetchone()
            if result:
                username, points, is_vip = result
                status = "✅" if (points > 999999 and is_vip) else "❌"
                print(f"   {status} {admin_id} (@{username}): {points:,} points, VIP: {is_vip}")
                if not (points > 999999 and is_vip):
                    all_admins_ok = False
            else:
                print(f"   ⚠️ {admin_id}: Not in database yet")
        
        # 5. Regular Users Status
        print(f"\n5️⃣ Regular Users Status:")
        cursor.execute("SELECT COUNT(*) FROM users WHERE search_points = 1 AND is_vip = 0")
        regular_users = cursor.fetchone()[0]
        print(f"   ✅ Users with 1 point: {regular_users}")
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE search_points > 100 AND user_id NOT IN ({})".format(','.join(['?' for _ in ADMIN_IDS])), ADMIN_IDS)
        high_point_users = cursor.fetchone()[0]
        print(f"   ✅ Non-admin users with >100 points: {high_point_users}")
        
        # 6. VIP System Status
        print(f"\n6️⃣ VIP System Status:")
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_vip = 1")
        total_vips = cursor.fetchone()[0]
        print(f"   ✅ Total VIP users: {total_vips}")
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_vip = 1 AND user_id NOT IN ({})".format(','.join(['?' for _ in ADMIN_IDS])), ADMIN_IDS)
        non_admin_vips = cursor.fetchone()[0]
        print(f"   ✅ Non-admin VIPs: {non_admin_vips} (should be 0)")
        
        # 7. Referral System Status
        print(f"\n7️⃣ Referral System Status:")
        cursor.execute("SELECT COUNT(*) FROM referrals")
        total_referrals = cursor.fetchone()[0]
        print(f"   ✅ Total referrals: {total_referrals} (should be 0)")
        
        # 8. Clean Data Status
        print(f"\n8️⃣ Clean Data Status:")
        cursor.execute("SELECT COUNT(*) FROM favorites")
        favorites = cursor.fetchone()[0]
        print(f"   ✅ Favorites: {favorites} (should be 0)")
        
        cursor.execute("SELECT COUNT(*) FROM promo_code_uses")
        promo_uses = cursor.fetchone()[0]
        print(f"   ✅ Promo code uses: {promo_uses} (should be 0)")
        
        cursor.execute("SELECT COUNT(*) FROM user_notifications")
        notifications = cursor.fetchone()[0]
        print(f"   ✅ User notifications: {notifications} (should be 0)")
        
        # 9. Username Check
        print(f"\n9️⃣ Username Verification:")
        cursor.execute("SELECT user_id, username FROM users WHERE user_id = 5421243466")
        result = cursor.fetchone()
        if result:
            user_id, username = result
            if username == 'nerwa_degme':
                print(f"   ✅ User 5421243466: @{username} (correct)")
            else:
                print(f"   ❌ User 5421243466: @{username} (should be nerwa_degme)")
        
        # 10. Overall Assessment
        print(f"\n🔟 Overall Assessment:")
        
        issues = []
        if not all_admins_ok:
            issues.append("Some admin privileges not set correctly")
        if non_admin_vips > 0:
            issues.append(f"{non_admin_vips} non-admin VIPs still exist")
        if total_referrals > 0:
            issues.append(f"{total_referrals} referrals still exist")
        if favorites > 0 or promo_uses > 0 or notifications > 0:
            issues.append("Some user data not fully cleaned")
        
        if not issues:
            print("   🎉 PERFECT! ALL SYSTEMS CLEAN AND READY!")
            print("   ✅ Bot completely reset for new owner")
            print("   ✅ Only new admins have privileges")
            print("   ✅ All user data properly reset")
            print("   ✅ Phone database preserved")
            print("   ✅ Ready for handover to @TMCELLadmin")
            success = True
        else:
            print("   ⚠️ Minor issues found:")
            for issue in issues:
                print(f"      - {issue}")
            success = False
        
        conn.close()
        return success
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Final Status Check")
    
    success = final_status_check()
    
    if success:
        print(f"\n🎊 CONGRATULATIONS! 🎊")
        print(f"Your TM CELL bot is 100% ready for the new owner!")
        print(f"")
        print(f"📋 Handover Summary:")
        print(f"   🤖 Bot: @tmcell993bot")
        print(f"   👑 New Owner: @TMCELLadmin (7772025660)")
        print(f"   👮 Admins: @Arslan_Vpns, @rnxGG, @nerwa_degme")
        print(f"   📱 Phone Database: 3M+ records intact")
        print(f"   🧹 User Data: Completely reset")
        print(f"   💎 VIP System: Ready for new sales")
        print(f"   🔗 Referral System: Fresh start")
        print(f"")
        print(f"🚀 The bot is ready to be handed over!")
    else:
        print(f"\n⚠️ Please review the issues above before handover.")
    
    exit(0 if success else 1)

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to find and clean all old admin/owner data from VL2024.sqlite
"""

import sqlite3
import os
import sys
from datetime import datetime
from config import ADMIN_IDS, OWNER_ID

def analyze_database():
    """Analyze database for old admin/owner data"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Analyzing VL2024.sqlite for old admin/owner data...")
        print(f"Current admins (to keep): {ADMIN_IDS}")
        
        # Old admin/owner IDs to clean
        old_admin_ids = [
            5702253746,  # @Thekingmuslim
            6404812540,  # @Eziz_Official  
            7440280333,  # @a_nazarow77
            8046165511,  # @Aylar_kaa
        ]
        
        old_usernames = [
            'thekingmuslim', 'eziz_official', 'a_nazarow77', 'aylar_kaa',
            'nerwa_degme'  # Old owner username
        ]
        
        print(f"Old admin IDs to clean: {old_admin_ids}")
        print(f"Old usernames to clean: {old_usernames}")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"\n📊 Found {len(tables)} tables:")
        
        old_admin_data = {}
        
        for table in tables:
            table_name = table[0]
            print(f"\n📋 Analyzing table: {table_name}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # Skip phone data tables
            if table_name.startswith('Tel'):
                print(f"   ⏭️ Skipping phone data table")
                continue
            
            print(f"   Columns: {column_names}")
            
            # Look for user_id columns
            if 'user_id' in column_names:
                # Find old admin data
                for old_id in old_admin_ids:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE user_id = ?", (old_id,))
                    count = cursor.fetchone()[0]
                    if count > 0:
                        print(f"   🔍 Found {count} records for old admin {old_id}")
                        if table_name not in old_admin_data:
                            old_admin_data[table_name] = []
                        old_admin_data[table_name].append(('user_id', old_id, count))
            
            # Look for username columns
            username_cols = [col for col in column_names if 'username' in col.lower() or 'name' in col.lower()]
            for col in username_cols:
                for old_username in old_usernames:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE LOWER({col}) LIKE ?", (f'%{old_username.lower()}%',))
                        count = cursor.fetchone()[0]
                        if count > 0:
                            print(f"   🔍 Found {count} records with username '{old_username}' in column '{col}'")
                            if table_name not in old_admin_data:
                                old_admin_data[table_name] = []
                            old_admin_data[table_name].append((col, old_username, count))
                    except sqlite3.Error:
                        continue
            
            # Look for VIP data
            vip_cols = [col for col in column_names if 'vip' in col.lower()]
            if vip_cols:
                for col in vip_cols:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {col} = 1 OR {col} = 'true'")
                        vip_count = cursor.fetchone()[0]
                        if vip_count > 0:
                            print(f"   👑 Found {vip_count} VIP records in column '{col}'")
                    except sqlite3.Error:
                        continue
        
        print(f"\n📋 SUMMARY OF OLD ADMIN DATA FOUND:")
        if old_admin_data:
            for table, data_list in old_admin_data.items():
                print(f"\n🗂️ Table: {table}")
                for col, value, count in data_list:
                    print(f"   - {col} = {value}: {count} records")
        else:
            print("✅ No old admin data found!")
        
        conn.close()
        return old_admin_data
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return {}
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return {}

def clean_old_admin_data(old_admin_data):
    """Clean old admin/owner data from database"""
    
    db_path = "VL2024.sqlite"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧹 Cleaning old admin/owner data...")
        
        # Old admin/owner IDs to clean
        old_admin_ids = [5702253746, 6404812540, 7440280333, 8046165511]
        old_usernames = ['thekingmuslim', 'eziz_official', 'a_nazarow77', 'aylar_kaa', 'nerwa_degme']
        
        cleaned_count = 0
        
        for table_name, data_list in old_admin_data.items():
            print(f"\n🗂️ Cleaning table: {table_name}")
            
            for col, value, count in data_list:
                try:
                    if col == 'user_id' and isinstance(value, int):
                        # Don't delete, just reset their privileges
                        if table_name == 'users':
                            cursor.execute(f"""
                                UPDATE {table_name} 
                                SET search_points = 1, 
                                    is_vip = 0, 
                                    vip_expiry_date = NULL
                                WHERE user_id = ?
                            """, (value,))
                            print(f"   ✅ Reset privileges for user_id {value}")
                        else:
                            # For other tables, we might want to keep the data but reset values
                            print(f"   ℹ️ Keeping data for user_id {value} in {table_name}")
                    
                    elif isinstance(value, str):
                        # Update username references
                        if 'username' in col.lower():
                            cursor.execute(f"""
                                UPDATE {table_name} 
                                SET {col} = 'old_user_' || {col}
                                WHERE LOWER({col}) LIKE ?
                            """, (f'%{value.lower()}%',))
                            print(f"   ✅ Updated {cursor.rowcount} username references")
                            cleaned_count += cursor.rowcount
                    
                except sqlite3.Error as e:
                    print(f"   ❌ Error cleaning {col}={value}: {e}")
        
        # Reset VIP status for old admins
        print(f"\n👑 Resetting VIP status for old admins...")
        for old_id in old_admin_ids:
            cursor.execute("""
                UPDATE users 
                SET is_vip = 0, 
                    vip_expiry_date = NULL,
                    search_points = 1
                WHERE user_id = ?
            """, (old_id,))
            if cursor.rowcount > 0:
                print(f"   ✅ Reset VIP for user_id {old_id}")
        
        # Ensure current admins have unlimited privileges
        print(f"\n👑 Setting unlimited privileges for current admins...")
        for admin_id in ADMIN_IDS:
            cursor.execute("""
                UPDATE users 
                SET search_points = 999999999,
                    is_vip = 1,
                    vip_expiry_date = '2099-12-31'
                WHERE user_id = ?
            """, (admin_id,))
            if cursor.rowcount > 0:
                print(f"   ✅ Set unlimited privileges for admin {admin_id}")
        
        conn.commit()
        
        print(f"\n🎉 Cleaning completed!")
        print(f"📊 Summary:")
        print(f"   - Records cleaned/updated: {cleaned_count}")
        print(f"   - Old admin privileges reset")
        print(f"   - Current admin privileges confirmed")
        print(f"📅 Cleaned at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Old Admin Data Cleaner")
    print("=" * 60)
    
    # Analyze first
    old_data = analyze_database()
    
    if old_data:
        response = input("\n⚠️ Clean old admin/owner data? (yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            clean_old_admin_data(old_data)
        else:
            print("❌ Cleaning cancelled")
    else:
        print("\n✅ No old admin data found to clean!")
    
    print("\n✅ Analysis complete!")

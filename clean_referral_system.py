#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to clean referral system from old admin/owner influence
"""

import sqlite3
import os
import sys
from datetime import datetime
from config import ADMIN_IDS

def analyze_referral_system():
    """Analyze referral system for old admin influence"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Analyzing referral system...")
        
        # Old admin IDs
        old_admin_ids = [5702253746, 6404812540, 7440280333, 8046165511]
        
        # Check referrals table
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='referrals'")
        if not cursor.fetchone():
            print("❌ Referrals table not found")
            return
        
        # Get top referrers
        cursor.execute("""
            SELECT referrer_id, COUNT(*) as referral_count
            FROM referrals 
            GROUP BY referrer_id 
            ORDER BY referral_count DESC 
            LIMIT 100
        """)
        
        top_referrers = cursor.fetchall()
        
        print(f"\n📊 Top 100 referrers:")
        old_admin_referrals = []
        
        for i, (referrer_id, count) in enumerate(top_referrers, 1):
            # Get user info
            cursor.execute("SELECT username FROM users WHERE user_id = ?", (referrer_id,))
            result = cursor.fetchone()
            username = result[0] if result else 'unknown'
            
            status = ""
            if referrer_id in ADMIN_IDS:
                status = "👑 CURRENT ADMIN"
            elif referrer_id in old_admin_ids:
                status = "❌ OLD ADMIN"
                old_admin_referrals.append((referrer_id, username, count))
            else:
                status = "👤 USER"
            
            print(f"   {i:2d}. {status} {referrer_id} (@{username}): {count} referrals")
        
        # Check if old admins referred people
        print(f"\n🔍 Old admin referral influence:")
        if old_admin_referrals:
            for referrer_id, username, count in old_admin_referrals:
                print(f"   ❌ Old admin {referrer_id} (@{username}) referred {count} users")
                
                # Get list of people they referred
                cursor.execute("""
                    SELECT referred_id, join_date 
                    FROM referrals 
                    WHERE referrer_id = ? 
                    ORDER BY join_date DESC
                """, (referrer_id,))
                
                referred_users = cursor.fetchall()
                print(f"      Users referred by this old admin:")
                for referred_id, join_date in referred_users[:10]:  # Show first 10
                    cursor.execute("SELECT username FROM users WHERE user_id = ?", (referred_id,))
                    result = cursor.fetchone()
                    ref_username = result[0] if result else 'unknown'
                    print(f"        - {referred_id} (@{ref_username}) on {join_date}")
                
                if len(referred_users) > 10:
                    print(f"        ... and {len(referred_users) - 10} more")
        else:
            print("   ✅ No old admin referral influence found")
        
        # Check if old admins were referred by someone
        print(f"\n🔍 Who referred the old admins:")
        for old_id in old_admin_ids:
            cursor.execute("SELECT referrer_id FROM referrals WHERE referred_id = ?", (old_id,))
            result = cursor.fetchone()
            if result:
                referrer_id = result[0]
                cursor.execute("SELECT username FROM users WHERE user_id = ?", (referrer_id,))
                ref_result = cursor.fetchone()
                ref_username = ref_result[0] if ref_result else 'unknown'
                print(f"   Old admin {old_id} was referred by {referrer_id} (@{ref_username})")
            else:
                print(f"   Old admin {old_id} was not referred by anyone")
        
        conn.close()
        return old_admin_referrals
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return []
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return []

def clean_referral_influence(old_admin_referrals):
    """Clean old admin influence from referral system"""
    
    db_path = "VL2024.sqlite"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🧹 Cleaning old admin referral influence...")
        
        old_admin_ids = [5702253746, 6404812540, 7440280333, 8046165511]
        
        cleaned_referrals = 0
        
        # Option 1: Remove referral records where old admin was the referrer
        for referrer_id, username, count in old_admin_referrals:
            print(f"\n🔄 Processing old admin {referrer_id} (@{username}) with {count} referrals...")
            
            # Get all users referred by this old admin
            cursor.execute("SELECT referred_id FROM referrals WHERE referrer_id = ?", (referrer_id,))
            referred_users = cursor.fetchall()
            
            # Option A: Delete referral records (breaks referral chain)
            # cursor.execute("DELETE FROM referrals WHERE referrer_id = ?", (referrer_id,))
            
            # Option B: Reassign referrals to current owner (better for users)
            current_owner = ADMIN_IDS[0]  # First admin (owner)
            cursor.execute("""
                UPDATE referrals 
                SET referrer_id = ? 
                WHERE referrer_id = ?
            """, (current_owner, referrer_id))
            
            updated_count = cursor.rowcount
            print(f"   ✅ Reassigned {updated_count} referrals to current owner {current_owner}")
            cleaned_referrals += updated_count
        
        # Remove old admins from being referred (if they were referred by someone)
        for old_id in old_admin_ids:
            cursor.execute("DELETE FROM referrals WHERE referred_id = ?", (old_id,))
            if cursor.rowcount > 0:
                print(f"   ✅ Removed referral record for old admin {old_id}")
        
        conn.commit()
        
        print(f"\n🎉 Referral system cleaned!")
        print(f"📊 Summary:")
        print(f"   - Referrals reassigned to current owner: {cleaned_referrals}")
        print(f"   - Old admin referral records removed")
        print(f"📅 Cleaned at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def analyze_vip_buyers():
    """Analyze VIP buyers for old admin influence"""
    
    db_path = "VL2024.sqlite"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n👑 Analyzing VIP buyers...")
        
        old_admin_ids = [5702253746, 6404812540, 7440280333, 8046165511]
        
        # Get all VIP users
        cursor.execute("""
            SELECT user_id, username, is_vip, vip_expiry_date, search_points
            FROM users 
            WHERE is_vip = 1 
            ORDER BY vip_expiry_date DESC
        """)
        
        vip_users = cursor.fetchall()
        
        print(f"📊 Found {len(vip_users)} VIP users:")
        
        old_admin_vips = []
        
        for user_id, username, is_vip, expiry_date, points in vip_users:
            status = ""
            if user_id in ADMIN_IDS:
                status = "👑 CURRENT ADMIN"
            elif user_id in old_admin_ids:
                status = "❌ OLD ADMIN VIP"
                old_admin_vips.append((user_id, username, expiry_date, points))
            else:
                status = "👤 REGULAR VIP"
            
            print(f"   {status} {user_id} (@{username}): expires {expiry_date}, {points} points")
        
        if old_admin_vips:
            print(f"\n❌ Found {len(old_admin_vips)} old admin VIPs to clean")
            return old_admin_vips
        else:
            print(f"\n✅ No old admin VIPs found")
            return []
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        return []
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return []

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Referral & VIP System Cleaner")
    print("=" * 60)
    
    # Analyze referral system
    old_referrals = analyze_referral_system()
    
    # Analyze VIP buyers
    old_vips = analyze_vip_buyers()
    
    if old_referrals or old_vips:
        response = input("\n⚠️ Clean old admin influence from referral/VIP systems? (yes/no): ").lower().strip()
        if response in ['yes', 'y']:
            if old_referrals:
                clean_referral_influence(old_referrals)
            if old_vips:
                print("\n👑 Old admin VIP status already cleaned in previous step")
        else:
            print("❌ Cleaning cancelled")
    else:
        print("\n✅ No old admin influence found in referral/VIP systems!")
    
    print("\n✅ Analysis complete!")

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.keyboards import get_vip_keyboard, get_vip_dealer_keyboard, get_back_button
from utils.languages import get_message
from config import OWNER_CONTACT

def vip_callback(update: Update, context: CallbackContext):
    """Handle VIP-related callbacks"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    if query.data == "vip_buy":
        # Show VIP purchase options
        query.edit_message_text(
            get_message('vip_title', language),
            reply_markup=get_vip_keyboard(language)
        )

    elif query.data == "vip_tmcell":
        # Show TMCELL dealer options
        query.edit_message_text(
            get_message('vip_dealer', language),
            reply_markup=get_vip_dealer_keyboard(language)
        )

    elif query.data == "vip_active":
        # Show VIP status
        query.answer("You already have VIP status!")

    elif query.data == "vip_owner":
        # Redirect to owner's contact
        query.answer("Redirecting to bot owner...")
        context.bot.send_message(
            chat_id=user_id,
            text=f"Contact the bot owner: {OWNER_CONTACT}"
        )

    # Legacy VIP options handling
    elif query.data.startswith("vip_"):
        period = query.data.split("_")[1]

        if period in ["1month", "3months", "6months"]:
            prices = {
                "1month": 50,
                "3months": 120,
                "6months": 200
            }
            price = prices.get(period, 0)

            # Get payment text based on language
            if language == 'ru':
                payment_text = f"""
💳 Платежная информация:

🏦 Счет: TM CELL
👤 Получатель:
💰 Сумма: {price} TMT

После оплаты отправьте скриншот администратору.
                """
            elif language == 'en':
                payment_text = f"""
💳 Payment Information:

🏦 Account: TM CELL
👤 Recipient:
💰 Amount: {price} TMT

After payment, please send a screenshot to the admin.
                """
            else:
                payment_text = f"""
💳 Töleg maglumatlary:

🏦 Hasap: TM CELL
👤 Alyjy:
💰 Möçberi: {price} TMT

Töleg edenizden soň skrini admina iberiň.
                """

            # Create keyboard based on language
            if language == 'ru':
                keyboard = [
                    [InlineKeyboardButton("Владелец 👑", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton("Админ 👨‍💻", url="https://t.me/nerwa_degme")],
                    [InlineKeyboardButton("2 Админ 👨‍💻", url="https://t.me/Arslan_Vpns")],
                    [InlineKeyboardButton("3 Админ 👨‍💻", url="https://t.me/rnxGG")],
                    [InlineKeyboardButton("Техническая поддержка 👮‍♂️", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton(get_message('back', language), callback_data="vip_buy")]
                ]
            elif language == 'en':
                keyboard = [
                    [InlineKeyboardButton("Owner 👑", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton("Admin 👨‍💻", url="https://t.me/nerwa_degme")],
                    [InlineKeyboardButton("2 Admin 👨‍💻", url="https://t.me/Arslan_Vpns")],
                    [InlineKeyboardButton("3 Admin 👨‍💻", url="https://t.me/rnxGG")],
                    [InlineKeyboardButton("Technical Support 👮‍♂️", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton(get_message('back', language), callback_data="vip_buy")]
                ]
            else:
                keyboard = [
                    [InlineKeyboardButton("Owner 👑", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton("Admin 👨‍💻", url="https://t.me/nerwa_degme")],
                    [InlineKeyboardButton("2 Admin 👨‍💻", url="https://t.me/Arslan_Vpns")],
                    [InlineKeyboardButton("3 Admin 👨‍💻", url="https://t.me/rnxGG")],
                    [InlineKeyboardButton("Tehniki Kömek 👮‍♂️", url="https://t.me/TMCELLadmin")],
                    [InlineKeyboardButton(get_message('back', language), callback_data="vip_buy")]
                ]

            query.edit_message_text(
                payment_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
"""
Admin panel functionality for the bot
"""
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from utils.constants import ParseMode
from telegram.ext import CallbackContext, Conversation<PERSON>andler
from config import ADMIN_IDS, OWNER_ID
from utils.db import Database
from utils.languages import get_message
import datetime
import json

# Admin panel states
ADMIN_MAIN, ADMIN_STATS, ADMIN_USERS, ADMIN_VIP, ADMIN_BROADCAST, ADMIN_SETTINGS = range(6)

def admin_panel(update: Update, context: CallbackContext) -> int:
    """Show admin panel main menu"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Get admin panel title based on language and user type
    # Different title for owner vs regular admins
    if user_id == OWNER_ID:
        # Owner panel title
        if language == 'ru':
            title = "👑 <b>Панель владельца</b>"
        elif language == 'en':
            title = "👑 <b>Owner Panel</b>"
        else:
            title = "👑 <b>Bot Eýesi</b>"
    else:
        # Admin panel title
        if language == 'ru':
            title = "👑 <b>Панель администратора</b>"
        elif language == 'en':
            title = "👑 <b>Admin Panel</b>"
        else:
            title = "👑 <b>Admin Panel</b>"

    # Create admin panel keyboard with different options for owner vs regular admins
    if user_id == OWNER_ID:
        # Owner gets all options
        keyboard = [
            [InlineKeyboardButton("📊 Statistika", callback_data="admin_stats")],
            [InlineKeyboardButton("👥 Ulanyjylar", callback_data="admin_users")],
            [InlineKeyboardButton("⭐ VIP dolandyryş", callback_data="admin_vip")],
            [InlineKeyboardButton("📢 Ähli ulanyjylara habar", callback_data="admin_broadcast")],
            [InlineKeyboardButton("⚙️ Admin sazlamalary", callback_data="admin_settings")],
            [InlineKeyboardButton("👤 Täze ulanyjylar", callback_data="new_users_30")],
            [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
        ]
    else:
        # Regular admins get limited options
        keyboard = [
            [InlineKeyboardButton("📊 Statistika", callback_data="admin_stats")],
            [InlineKeyboardButton("👥 Ulanyjylar", callback_data="admin_users")],
            [InlineKeyboardButton("⭐ VIP dolandyryş", callback_data="admin_vip")],
            [InlineKeyboardButton("⚙️ Admin sazlamalary", callback_data="admin_settings")],
            [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="main_menu")]
        ]

    # Answer callback query if this is a callback
    if update.callback_query:
        update.callback_query.answer()
        update.callback_query.edit_message_text(
            text=f"{title}\n\nAdmin panele hoş geldiňiz! Aşakdaky funksiýalardan birini saýlaň:",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.HTML
        )
    else:
        update.message.reply_text(
            text=f"{title}\n\nAdmin panele hoş geldiňiz! Aşakdaky funksiýalardan birini saýlaň:",
            reply_markup=InlineKeyboardMarkup(keyboard),
            parse_mode=ParseMode.HTML
        )

    return ADMIN_MAIN

def admin_stats(update: Update, context: CallbackContext) -> int:
    """Show admin statistics"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Answer the callback query
    update.callback_query.answer()

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Get statistics
    total_users = db.get_total_users_count()
    active_users = db.get_active_users_count()
    vip_users = db.get_vip_users_count()
    total_searches = db.get_total_searches_count()
    today_searches = db.get_today_searches_count()

    # Format statistics based on language
    if language == 'ru':
        stats_text = f"📊 <b>Статистика бота</b>\n\n"
        stats_text += f"👥 Всего пользователей: {total_users}\n"
        stats_text += f"👤 Активных пользователей: {active_users}\n"
        stats_text += f"⭐ VIP пользователей: {vip_users}\n\n"
        stats_text += f"🔍 Всего поисков: {total_searches}\n"
        stats_text += f"📅 Поисков сегодня: {today_searches}\n\n"
        stats_text += f"📆 Дата: {datetime.datetime.now().strftime('%d.%m.%Y %H:%M')}"
    elif language == 'en':
        stats_text = f"📊 <b>Bot Statistics</b>\n\n"
        stats_text += f"👥 Total users: {total_users}\n"
        stats_text += f"👤 Active users: {active_users}\n"
        stats_text += f"⭐ VIP users: {vip_users}\n\n"
        stats_text += f"🔍 Total searches: {total_searches}\n"
        stats_text += f"📅 Searches today: {today_searches}\n\n"
        stats_text += f"📆 Date: {datetime.datetime.now().strftime('%d.%m.%Y %H:%M')}"
    else:
        stats_text = f"📊 <b>Bot Statistikasy</b>\n\n"
        stats_text += f"👥 Jemi ulanyjylar: {total_users}\n"
        stats_text += f"👤 Aktiw ulanyjylar: {active_users}\n"
        stats_text += f"⭐ VIP ulanyjylar: {vip_users}\n\n"
        stats_text += f"🔍 Jemi gözlegler: {total_searches}\n"
        stats_text += f"📅 Şu günki gözlegler: {today_searches}\n\n"
        stats_text += f"📆 Sene: {datetime.datetime.now().strftime('%d.%m.%Y %H:%M')}"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("📈 Jikme-jik statistika", callback_data="admin_detailed_stats")],
        [InlineKeyboardButton("📊 Gözleg statistikasy", callback_data="search_stats")],
        [InlineKeyboardButton("🔄 Täzele", callback_data="admin_stats")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
    ]

    update.callback_query.answer()
    update.callback_query.edit_message_text(
        text=stats_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

    return ADMIN_STATS

def admin_users(update: Update, context: CallbackContext) -> int:
    """Show admin user management"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Answer the callback query
    update.callback_query.answer()

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Get recent users
    recent_users = db.get_recent_users(10)

    # Format user list based on language
    if language == 'ru':
        users_text = f"👥 <b>Управление пользователями</b>\n\n"
        users_text += f"<b>Последние 10 пользователей:</b>\n\n"
    elif language == 'en':
        users_text = f"👥 <b>User Management</b>\n\n"
        users_text += f"<b>Last 10 users:</b>\n\n"
    else:
        users_text = f"👥 <b>Ulanyjy dolandyryş</b>\n\n"
        users_text += f"<b>Soňky 10 ulanyjy:</b>\n\n"

    for user in recent_users:
        username = user.get('username', 'None')
        if username and not username.startswith('@'):
            username = f"@{username}"
        users_text += f"🆔 {user.get('user_id')} - {user.get('first_name')} {user.get('last_name', '')} ({username})\n"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("🔍 Ulanyjy gözle", callback_data="admin_search_user")],
        [InlineKeyboardButton("⭐ VIP ulanyjylar", callback_data="admin_vip_users")],
        [InlineKeyboardButton("🚫 Bloklanan ulanyjylar", callback_data="admin_blocked_users")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
    ]

    update.callback_query.answer()
    update.callback_query.edit_message_text(
        text=users_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

    return ADMIN_USERS

def admin_vip(update: Update, context: CallbackContext) -> int:
    """Show VIP management panel"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Answer the callback query
    update.callback_query.answer()

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Format VIP panel based on language
    if language == 'ru':
        vip_text = f"⭐ <b>Управление VIP</b>\n\n"
        vip_text += f"Здесь вы можете управлять VIP статусами пользователей.\n\n"
        vip_text += f"<b>Команды для выдачи VIP:</b>\n"
        vip_text += f"/set_vip_1 [user_id] - Выдать VIP на 1 месяц\n"
        vip_text += f"/set_vip_3 [user_id] - Выдать VIP на 3 месяца\n"
        vip_text += f"/set_vip_6 [user_id] - Выдать VIP на 6 месяцев\n"
        vip_text += f"/set_vip_perm [user_id] - Выдать постоянный VIP\n\n"
        vip_text += f"<b>Пример:</b> /set_vip_1 123456789"
    elif language == 'en':
        vip_text = f"⭐ <b>VIP Management</b>\n\n"
        vip_text += f"Here you can manage user VIP statuses.\n\n"
        vip_text += f"<b>Commands to grant VIP:</b>\n"
        vip_text += f"/set_vip_1 [user_id] - Grant VIP for 1 month\n"
        vip_text += f"/set_vip_3 [user_id] - Grant VIP for 3 months\n"
        vip_text += f"/set_vip_6 [user_id] - Grant VIP for 6 months\n"
        vip_text += f"/set_vip_perm [user_id] - Grant permanent VIP\n\n"
        vip_text += f"<b>Example:</b> /set_vip_1 123456789"
    else:
        vip_text = f"⭐ <b>VIP dolandyryş</b>\n\n"
        vip_text += f"Bu ýerde ulanyjylaryň VIP ýagdaýlaryny dolandyryp bilersiňiz.\n\n"
        vip_text += f"<b>VIP bermek üçin komandalar:</b>\n"
        vip_text += f"/set_vip_1 [user_id] - 1 aýlyk VIP bermek\n"
        vip_text += f"/set_vip_3 [user_id] - 3 aýlyk VIP bermek\n"
        vip_text += f"/set_vip_6 [user_id] - 6 aýlyk VIP bermek\n"
        vip_text += f"/set_vip_perm [user_id] - Hemişelik VIP bermek\n\n"
        vip_text += f"<b>Mysal:</b> /set_vip_1 123456789"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("⭐ VIP ulanyjylar", callback_data="admin_vip_users")],
        [InlineKeyboardButton("➕ VIP goş", callback_data="admin_add_vip")],
        [InlineKeyboardButton("➖ VIP aýyr", callback_data="admin_remove_vip")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
    ]

    update.callback_query.answer()
    update.callback_query.edit_message_text(
        text=vip_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

    return ADMIN_VIP

def admin_broadcast(update: Update, context: CallbackContext) -> int:
    """Show broadcast message panel"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Answer the callback query
    update.callback_query.answer()

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Format broadcast panel based on language
    if language == 'ru':
        broadcast_text = f"📢 <b>Отправка сообщения всем пользователям</b>\n\n"
        broadcast_text += f"Здесь вы можете отправить сообщение всем пользователям бота.\n\n"
        broadcast_text += f"<b>Команда для отправки:</b>\n"
        broadcast_text += f"/broadcast [сообщение]\n\n"
        broadcast_text += f"<b>Пример:</b> /broadcast Привет всем пользователям!"
    elif language == 'en':
        broadcast_text = f"📢 <b>Send Message to All Users</b>\n\n"
        broadcast_text += f"Here you can send a message to all bot users.\n\n"
        broadcast_text += f"<b>Command to send:</b>\n"
        broadcast_text += f"/broadcast [message]\n\n"
        broadcast_text += f"<b>Example:</b> /broadcast Hello to all users!"
    else:
        broadcast_text = f"📢 <b>Ähli ulanyjylara habar ibermek</b>\n\n"
        broadcast_text += f"Bu ýerde botyň ähli ulanyjylaryna habar iberip bilersiňiz.\n\n"
        broadcast_text += f"<b>Ibermek üçin komanda:</b>\n"
        broadcast_text += f"/broadcast [habar]\n\n"
        broadcast_text += f"<b>Mysal:</b> /broadcast Ähli ulanyjylara salam!"

    # Create keyboard
    keyboard = [
        [InlineKeyboardButton("📝 Täze habar", callback_data="admin_new_broadcast")],
        [InlineKeyboardButton("📊 Öňki habarlar", callback_data="admin_past_broadcasts")],
        [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
    ]

    update.callback_query.answer()
    update.callback_query.edit_message_text(
        text=broadcast_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

    return ADMIN_BROADCAST

def admin_settings(update: Update, context: CallbackContext) -> int:
    """Show admin settings panel"""
    user_id = update.effective_user.id

    # Check if user is admin
    if user_id not in ADMIN_IDS:
        update.callback_query.answer("Bu funksiýa diňe administratorlar üçin elýeterlidir.")
        return ConversationHandler.END

    # Answer the callback query
    update.callback_query.answer()

    # Get user's language
    db = Database()
    user_info = db.get_user_info(user_id)
    language = user_info.get('language', 'tm')

    # Format settings panel based on language
    if language == 'ru':
        settings_text = f"⚙️ <b>Настройки администратора</b>\n\n"
        settings_text += f"Здесь вы можете управлять настройками бота.\n\n"
    elif language == 'en':
        settings_text = f"⚙️ <b>Admin Settings</b>\n\n"
        settings_text += f"Here you can manage bot settings.\n\n"
    else:
        settings_text = f"⚙️ <b>Admin sazlamalary</b>\n\n"
        settings_text += f"Bu ýerde bot sazlamalaryny dolandyryp bilersiňiz.\n\n"

    # Create keyboard with different options for owner vs regular admins
    if user_id == OWNER_ID:
        # Owner gets all options including admin management
        keyboard = [
            [InlineKeyboardButton("👑 Täze admin goş", callback_data="new_admin")],
            [InlineKeyboardButton("🔄 Telefon maglumatlaryny täzele", callback_data="reload")],
            [InlineKeyboardButton("🔄 Boty täzeden başlat", callback_data="restart")],
            [InlineKeyboardButton("📊 Bot ýagdaýyny barla", callback_data="status")],
            [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
        ]
    else:
        # Regular admins get limited options
        keyboard = [
            [InlineKeyboardButton("🔄 Telefon maglumatlaryny täzele", callback_data="reload")],
            [InlineKeyboardButton("📊 Bot ýagdaýyny barla", callback_data="status")],
            [InlineKeyboardButton("🔙 " + get_message('back', language), callback_data="admin_panel")]
        ]

    update.callback_query.answer()
    update.callback_query.edit_message_text(
        text=settings_text,
        reply_markup=InlineKeyboardMarkup(keyboard),
        parse_mode=ParseMode.HTML
    )

    return ADMIN_SETTINGS

def register_admin_panel_handlers(dispatcher):
    """Register all admin panel handlers"""
    from telegram.ext import CommandHandler, CallbackQueryHandler

    # Register command handler
    dispatcher.add_handler(CommandHandler("admin", admin_panel))

    # Register callback handlers
    dispatcher.add_handler(CallbackQueryHandler(admin_panel, pattern="^admin_panel$"))
    dispatcher.add_handler(CallbackQueryHandler(admin_stats, pattern="^admin_stats$"))
    dispatcher.add_handler(CallbackQueryHandler(admin_users, pattern="^admin_users$"))
    dispatcher.add_handler(CallbackQueryHandler(admin_vip, pattern="^admin_vip$"))
    dispatcher.add_handler(CallbackQueryHandler(admin_broadcast, pattern="^admin_broadcast$"))
    dispatcher.add_handler(CallbackQueryHandler(admin_settings, pattern="^admin_settings$"))

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify new bot configuration
"""

import requests
import sys
from config import TOKEN, ADMIN_IDS, OWNER_ID, BOT_USERNAME, SUPPORT_CONTACT

def test_bot_token():
    """Test if bot token is valid"""
    print("🔑 Testing bot token...")
    
    try:
        url = f"https://api.telegram.org/bot{TOKEN}/getMe"
        response = requests.get(url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            if data.get('ok'):
                bot_info = data.get('result', {})
                print(f"✅ Bot token is valid!")
                print(f"   Bot ID: {bot_info.get('id')}")
                print(f"   Bot Name: {bot_info.get('first_name')}")
                print(f"   Bot Username: @{bot_info.get('username')}")
                print(f"   Can Join Groups: {bot_info.get('can_join_groups')}")
                print(f"   Can Read Messages: {bot_info.get('can_read_all_group_messages')}")
                return True
            else:
                print(f"❌ Bot token invalid: {data.get('description')}")
                return False
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Network error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_config_values():
    """Test configuration values"""
    print("\n⚙️ Testing configuration values...")
    
    # Test token format
    if TOKEN and len(TOKEN.split(':')) == 2:
        bot_id, bot_token = TOKEN.split(':')
        if bot_id.isdigit() and len(bot_token) == 35:
            print("✅ Token format is correct")
        else:
            print("❌ Token format is incorrect")
    else:
        print("❌ Token format is invalid")
    
    # Test admin IDs
    print(f"👑 Owner ID: {OWNER_ID}")
    print(f"👮 Admin IDs: {ADMIN_IDS}")
    
    if OWNER_ID in ADMIN_IDS:
        print("✅ Owner is in admin list")
    else:
        print("⚠️ Owner is not in admin list")
    
    # Test other config
    print(f"🤖 Bot Username: {BOT_USERNAME}")
    print(f"🆘 Support Contact: {SUPPORT_CONTACT}")
    
    # Verify new values
    expected_values = {
        'OWNER_ID': 7772025660,
        'BOT_USERNAME': '@tmcell993bot',
        'SUPPORT_CONTACT': '@TMCELLadmin'
    }
    
    all_correct = True
    for key, expected in expected_values.items():
        actual = globals().get(key)
        if actual == expected:
            print(f"✅ {key}: {actual}")
        else:
            print(f"❌ {key}: Expected {expected}, got {actual}")
            all_correct = False
    
    return all_correct

def test_admin_ids():
    """Test admin IDs"""
    print("\n👮 Testing admin IDs...")
    
    expected_admins = [7772025660, 7027624995, 6687750461, 5421243466]
    
    print("Expected admins:")
    admin_names = {
        7772025660: "@TMCELLadmin (Owner)",
        7027624995: "@Arslan_Vpns", 
        6687750461: "@rnxGG",
        5421243466: "@nerwa_degme"
    }
    
    for admin_id in expected_admins:
        name = admin_names.get(admin_id, "Unknown")
        if admin_id in ADMIN_IDS:
            print(f"✅ {admin_id} - {name}")
        else:
            print(f"❌ {admin_id} - {name} (MISSING)")
    
    # Check for unexpected admins
    for admin_id in ADMIN_IDS:
        if admin_id not in expected_admins:
            print(f"⚠️ Unexpected admin: {admin_id}")
    
    return set(ADMIN_IDS) == set(expected_admins)

def main():
    """Main test function"""
    print("🚀 TM CELL Bot - Configuration Test")
    print("=" * 50)
    
    # Run tests
    token_ok = test_bot_token()
    config_ok = test_config_values()
    admin_ok = test_admin_ids()
    
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS:")
    print(f"🔑 Token Test: {'✅ PASS' if token_ok else '❌ FAIL'}")
    print(f"⚙️ Config Test: {'✅ PASS' if config_ok else '❌ FAIL'}")
    print(f"👮 Admin Test: {'✅ PASS' if admin_ok else '❌ FAIL'}")
    
    if token_ok and config_ok and admin_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Bot is ready to use with new configuration")
        return True
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("⚠️ Please check the configuration")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

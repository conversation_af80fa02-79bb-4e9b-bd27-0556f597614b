#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>ript to reset all user points to 1, except for current admins
"""

import sqlite3
import os
import sys
from datetime import datetime
from config import ADMIN_IDS, OWNER_ID

def reset_user_points():
    """Reset all user points to 1, except for current admins"""
    
    # Database file path
    db_path = "user_data.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Resetting user points...")
        print(f"Current admins (will keep unlimited points): {ADMIN_IDS}")
        
        # Specific old admin/user IDs that need to be reset
        old_admin_users = [
            "thekingmuslim",  # Username
            "eziz_official",  # Username  
            "a_nazarow77",    # Username
            "Aylar_kaa"      # Username
        ]
        
        # Also reset by user IDs if we know them
        old_admin_ids = [
            6404812540,  # Old admin
            8046165511,  # Old admin
            7440280333,  # Old admin
            5702253746   # Old admin
        ]
        
        # Get all users with high points (likely old admins/special users)
        cursor.execute("""
            SELECT user_id, username, search_points 
            FROM users 
            WHERE search_points > 100
            ORDER BY search_points DESC
        """)
        
        high_point_users = cursor.fetchall()
        
        reset_count = 0
        kept_count = 0
        
        for user_id, username, points in high_point_users:
            # Skip current admins
            if user_id in ADMIN_IDS:
                print(f"✅ Keeping admin privileges: {user_id} (@{username or 'unknown'}) - {points} points")
                kept_count += 1
                continue
            
            # Reset old admins and special users
            should_reset = False
            
            # Check by user ID
            if user_id in old_admin_ids:
                should_reset = True
                print(f"🔄 Resetting old admin ID: {user_id}")
            
            # Check by username
            if username:
                username_lower = username.lower()
                for old_username in old_admin_users:
                    if old_username.lower() in username_lower:
                        should_reset = True
                        print(f"🔄 Resetting old admin username: @{username}")
                        break
            
            # Reset users with extremely high points (likely old admins)
            if points > 999999:
                should_reset = True
                print(f"🔄 Resetting user with excessive points: {user_id} (@{username or 'unknown'}) - {points} points")
            
            if should_reset:
                cursor.execute("""
                    UPDATE users 
                    SET search_points = 1,
                        is_vip = 0,
                        vip_expiry_date = NULL
                    WHERE user_id = ?
                """, (user_id,))
                reset_count += 1
                print(f"✅ Reset to 1 point: {user_id} (@{username or 'unknown'})")
        
        # Also reset ALL users to 1 point except current admins (safety measure)
        cursor.execute("""
            UPDATE users 
            SET search_points = 1
            WHERE user_id NOT IN ({})
        """.format(','.join(['?' for _ in ADMIN_IDS])), ADMIN_IDS)
        
        # Give unlimited points to current admins
        for admin_id in ADMIN_IDS:
            cursor.execute("""
                UPDATE users 
                SET search_points = 999999999,
                    is_vip = 1,
                    vip_expiry_date = '2099-12-31'
                WHERE user_id = ?
            """, (admin_id,))
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 User points reset completed!")
        print(f"📊 Summary:")
        print(f"   - High-point users reset: {reset_count}")
        print(f"   - Current admins kept: {kept_count}")
        print(f"   - All other users set to: 1 point")
        print(f"   - Current admins: unlimited points")
        print(f"📅 Reset at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Show current admin status
        print(f"\n👑 Current Admin Status:")
        for admin_id in ADMIN_IDS:
            cursor.execute("SELECT username, search_points FROM users WHERE user_id = ?", (admin_id,))
            result = cursor.fetchone()
            if result:
                username, points = result
                print(f"   ✅ {admin_id} (@{username or 'unknown'}): {points} points")
            else:
                print(f"   ⚠️ {admin_id}: Not in database yet")
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 TM CELL Bot - User Points Reset")
    print("=" * 50)
    
    # Confirm action
    response = input("⚠️ This will reset all user points to 1 (except current admins). Continue? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Operation cancelled")
        sys.exit(0)
    
    reset_user_points()
    print("\n✅ All done! User points normalized.")

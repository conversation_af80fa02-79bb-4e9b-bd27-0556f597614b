#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to check database structure and fix user points
"""

import sqlite3
import os
import sys
from config import ADMIN_IDS

def check_database_structure():
    """Check and show database structure"""
    
    db_path = "user_data.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking database structure...")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print(f"📊 Found {len(tables)} tables:")
        for table in tables:
            table_name = table[0]
            print(f"\n📋 Table: {table_name}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            
            print("   Columns:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, primary_key = col
                print(f"     - {col_name} ({col_type})")
            
            # Get row count
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            print(f"   Rows: {count}")
            
            # If it's users table, show some data
            if table_name == 'users' and count > 0:
                cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                rows = cursor.fetchall()
                print("   Sample data:")
                for row in rows:
                    print(f"     {row}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

def fix_user_points():
    """Fix user points using correct column names"""
    
    db_path = "user_data.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("\n🔧 Fixing user points...")
        
        # Check if users table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='users'")
        if not cursor.fetchone():
            print("❌ Users table not found")
            return
        
        # Get table structure
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"Available columns: {column_names}")
        
        # Find the correct column name for points
        points_column = None
        if 'search_points' in column_names:
            points_column = 'search_points'
        elif 'points' in column_names:
            points_column = 'points'
        elif 'balance' in column_names:
            points_column = 'balance'
        
        if not points_column:
            print("❌ No points column found. Adding search_points column...")
            cursor.execute("ALTER TABLE users ADD COLUMN search_points INTEGER DEFAULT 1")
            points_column = 'search_points'
        
        print(f"Using points column: {points_column}")
        
        # Get all users with their current points
        cursor.execute(f"SELECT user_id, username, {points_column} FROM users")
        users = cursor.fetchall()
        
        reset_count = 0
        admin_count = 0
        
        for user_id, username, current_points in users:
            if user_id in ADMIN_IDS:
                # Set unlimited points for admins
                cursor.execute(f"""
                    UPDATE users 
                    SET {points_column} = 999999999
                    WHERE user_id = ?
                """, (user_id,))
                print(f"✅ Admin unlimited points: {user_id} (@{username or 'unknown'})")
                admin_count += 1
            else:
                # Set 1 point for regular users
                cursor.execute(f"""
                    UPDATE users 
                    SET {points_column} = 1
                    WHERE user_id = ?
                """, (user_id,))
                if current_points != 1:
                    print(f"🔄 Reset to 1 point: {user_id} (@{username or 'unknown'}) - was {current_points}")
                    reset_count += 1
        
        conn.commit()
        
        print(f"\n🎉 User points fixed!")
        print(f"📊 Summary:")
        print(f"   - Users reset to 1 point: {reset_count}")
        print(f"   - Admins with unlimited points: {admin_count}")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Database Structure Check")
    print("=" * 50)
    
    check_database_structure()
    
    response = input("\n⚠️ Fix user points? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        fix_user_points()
    
    print("\n✅ Done!")

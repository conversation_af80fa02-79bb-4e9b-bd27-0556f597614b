#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to clear user data from database while keeping phone data intact
Only clears user-related tables, NOT the phone database
"""

import sqlite3
import os
import sys
from datetime import datetime

def clear_user_data():
    """Clear all user data from database while keeping phone data intact"""
    
    # Database file path
    db_path = "user_data.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found. Creating new database...")
        # If database doesn't exist, create it with empty tables
        create_empty_database()
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🗑️ Clearing user data from database...")
        print("⚠️ VL2024.sqlite (phone data) will NOT be touched!")
        
        # List of user-related tables to clear
        user_tables = [
            'users',
            'searches', 
            'search_history',
            'favorites',
            'notifications',
            'promo_codes',
            'referrals',
            'user_stats',
            'daily_points',
            'vip_users',
            'blocked_users',
            'user_sessions',
            'user_settings',
            'user_logs'
        ]
        
        # Clear each table
        cleared_tables = []
        for table in user_tables:
            try:
                # Check if table exists
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                if cursor.fetchone():
                    # Get count before clearing
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    
                    if count > 0:
                        # Clear the table
                        cursor.execute(f"DELETE FROM {table}")
                        print(f"✅ Cleared {table}: {count} records removed")
                        cleared_tables.append(f"{table} ({count} records)")
                    else:
                        print(f"ℹ️ {table}: already empty")
                else:
                    print(f"ℹ️ {table}: table not found")
                    
            except sqlite3.Error as e:
                print(f"❌ Error clearing {table}: {e}")
        
        # Reset auto-increment counters
        try:
            cursor.execute("DELETE FROM sqlite_sequence WHERE name IN ({})".format(
                ','.join(['?' for _ in user_tables])
            ), user_tables)
            print("✅ Reset auto-increment counters")
        except sqlite3.Error:
            pass  # sqlite_sequence might not exist
        
        # Commit changes
        conn.commit()
        
        # Vacuum database to reclaim space
        cursor.execute("VACUUM")
        print("✅ Database optimized")
        
        print(f"\n🎉 User data cleared successfully!")
        print(f"📊 Summary: {len(cleared_tables)} tables cleared")
        for table_info in cleared_tables:
            print(f"   - {table_info}")
        
        print(f"\n⚠️ IMPORTANT: VL2024.sqlite (phone database) was NOT modified!")
        print(f"📅 Cleared at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

def create_empty_database():
    """Create empty database with user tables"""
    try:
        conn = sqlite3.connect("user_data.db")
        cursor = conn.cursor()
        
        print("📝 Creating empty user database...")
        
        # Create basic user tables (empty)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER UNIQUE NOT NULL,
                username TEXT,
                first_name TEXT,
                last_name TEXT,
                language_code TEXT DEFAULT 'tm',
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_vip BOOLEAN DEFAULT FALSE,
                points INTEGER DEFAULT 0,
                searches_today INTEGER DEFAULT 0,
                total_searches INTEGER DEFAULT 0,
                is_blocked BOOLEAN DEFAULT FALSE
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS searches (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                search_query TEXT NOT NULL,
                search_type TEXT NOT NULL,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                results_found INTEGER DEFAULT 0,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)
        
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS search_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                search_query TEXT NOT NULL,
                search_result TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (user_id)
            )
        """)
        
        conn.commit()
        print("✅ Empty database created successfully")
        
    except sqlite3.Error as e:
        print(f"❌ Error creating database: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 TM CELL Bot - User Data Cleaner")
    print("=" * 50)
    
    # Confirm action
    response = input("⚠️ This will clear ALL user data. Continue? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Operation cancelled")
        sys.exit(0)
    
    clear_user_data()
    print("\n✅ All done! Bot is ready for new users.")

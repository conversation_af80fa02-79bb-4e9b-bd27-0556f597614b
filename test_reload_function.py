#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test reload function
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_reload_function():
    """Test reload function logic"""
    
    print("🧪 Testing Reload Function")
    print("=" * 50)
    
    try:
        # Test database connection
        from utils.db import Database
        print("✅ Database import successful")
        
        # Test config import
        from config import ADMIN_IDS, OWNER_ID
        print(f"✅ Config import successful")
        print(f"   Admin IDs: {ADMIN_IDS}")
        print(f"   Owner ID: {OWNER_ID}")
        
        # Test database connection
        db = Database()
        print("✅ Database connection successful")
        
        # Test admin check
        print(f"\n📋 Testing admin functionality:")
        for admin_id in ADMIN_IDS:
            exists = db.user_exists(admin_id)
            print(f"   Admin {admin_id}: {'exists' if exists else 'not found'}")
        
        # Close database
        db.close()
        print("✅ Database closed successfully")
        
        print(f"\n🎉 Reload function components working!")
        print(f"✅ Database operations: OK")
        print(f"✅ Config loading: OK") 
        print(f"✅ Admin verification: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing reload function: {e}")
        return False

if __name__ == "__main__":
    success = test_reload_function()
    if success:
        print(f"\n🚀 Reload function is ready to use!")
        print(f"   - Use /reload command as admin")
        print(f"   - Or use 'Reload Bot' button in admin panel")
    else:
        print(f"\n⚠️ Reload function needs fixing")
    
    exit(0 if success else 1)

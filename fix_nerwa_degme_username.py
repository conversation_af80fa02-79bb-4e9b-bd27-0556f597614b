#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix @old_user_nerwa_degme back to @nerwa_degme
"""

import sqlite3
import os
import sys
from datetime import datetime

def fix_nerwa_degme_username():
    """Fix nerwa_degme username back to original"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔧 Fixing @old_user_nerwa_degme back to @nerwa_degme...")
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        total_fixed = 0
        
        for table in tables:
            table_name = table[0]
            
            # Skip phone data tables
            if table_name.startswith('Tel'):
                continue
            
            print(f"\n📋 Checking table: {table_name}")
            
            # Get table structure
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            # Find username/name columns
            username_cols = [col for col in column_names if 'username' in col.lower() or 'name' in col.lower()]
            
            for col in username_cols:
                try:
                    # Check if there are old_user_nerwa_degme records
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {col} = ?", ('old_user_nerwa_degme',))
                    count = cursor.fetchone()[0]
                    
                    if count > 0:
                        print(f"   🔍 Found {count} records with 'old_user_nerwa_degme' in column '{col}'")
                        
                        # Fix the username
                        cursor.execute(f"""
                            UPDATE {table_name} 
                            SET {col} = 'nerwa_degme'
                            WHERE {col} = 'old_user_nerwa_degme'
                        """)
                        
                        fixed = cursor.rowcount
                        if fixed > 0:
                            print(f"   ✅ Fixed {fixed} records in {col}")
                            total_fixed += fixed
                    
                    # Also check for any other old_user_ prefixes for nerwa_degme
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE {col} LIKE ?", ('%old_user_%nerwa_degme%',))
                    count2 = cursor.fetchone()[0]
                    
                    if count2 > 0:
                        print(f"   🔍 Found {count2} records with 'old_user_*nerwa_degme' pattern in column '{col}'")
                        
                        # Fix any old_user_ prefixed nerwa_degme
                        cursor.execute(f"""
                            UPDATE {table_name} 
                            SET {col} = 'nerwa_degme'
                            WHERE {col} LIKE '%old_user_%nerwa_degme%'
                        """)
                        
                        fixed2 = cursor.rowcount
                        if fixed2 > 0:
                            print(f"   ✅ Fixed {fixed2} additional records in {col}")
                            total_fixed += fixed2
                
                except sqlite3.Error as e:
                    print(f"   ❌ Error checking {col}: {e}")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 Username fix completed!")
        print(f"📊 Summary:")
        print(f"   - Total records fixed: {total_fixed}")
        print(f"   - @old_user_nerwa_degme → @nerwa_degme")
        print(f"📅 Fixed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Verify the fix
        print(f"\n🔍 Verification:")
        cursor.execute("SELECT user_id, username FROM users WHERE user_id = 5421243466")
        result = cursor.fetchone()
        if result:
            user_id, username = result
            print(f"   ✅ User 5421243466 username: @{username}")
        else:
            print(f"   ⚠️ User 5421243466 not found")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Fix nerwa_degme Username")
    print("=" * 50)
    
    response = input("⚠️ Fix @old_user_nerwa_degme back to @nerwa_degme? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        fix_nerwa_degme_username()
    else:
        print("❌ Fix cancelled")
    
    print("\n✅ Operation complete!")

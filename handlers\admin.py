# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ParseMode
    from telegram.ext import CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class InlineKeyboardButton: pass
    class InlineKeyboardMarkup: pass
    class ParseMode: pass
from utils.db import Database
from utils.keyboards import get_back_button
from utils.languages import get_message
from config import ADMIN_IDS, OWNER_ID

def is_admin(user_id):
    """Check if a user is an admin"""
    return user_id in ADMIN_IDS

def is_main_admin(user_id):
    """Check if a user is the main admin (owner)"""
    return user_id == OWNER_ID

def admin_callback(update: Update, context: CallbackContext):
    """Handle admin panel callbacks"""
    # Check if this is a callback query or a command
    if hasattr(update, 'callback_query') and update.callback_query:
        query = update.callback_query
        user_id = query.from_user.id
        is_command = False
    else:
        # This is a command, not a callback query
        user_id = update.effective_user.id
        is_command = True

    # Check if user is admin
    if not is_admin(user_id):
        if not is_command:
            query.answer("You are not authorized to access this feature.")
        else:
            update.message.reply_text("You are not authorized to access this feature.")
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Handle different admin actions based on command or callback data
    if is_command or (not is_command and query.data == "admin_panel"):
        # Get user statistics
        total_users = db.get_total_users_count()
        vip_users = db.get_vip_users_count()

        # Show admin panel
        keyboard = [
            [InlineKeyboardButton("👥 Add Points to User", callback_data="admin_add_points"),
             InlineKeyboardButton("➖ Remove Points", callback_data="admin_remove_points")],
            [InlineKeyboardButton("📊 View User Stats", callback_data="admin_view_stats"),
             InlineKeyboardButton("👑 Set VIP Status", callback_data="admin_set_vip")],
            [InlineKeyboardButton("📢 Broadcast Message", callback_data="admin_broadcast"),
             InlineKeyboardButton("📬 Notifications", callback_data="admin_notifications")],
            [InlineKeyboardButton("🎁 Promo Codes", callback_data="admin_promo_codes"),
             InlineKeyboardButton("🔄 Reload Bot", callback_data="admin_reload")],
            [InlineKeyboardButton("📋 Admin Commands", callback_data="admin_commands")],
            [InlineKeyboardButton("⬅️ Back", callback_data="main_menu")]
        ]

        # Add admin label based on user type
        admin_title = "👑 Owner Panel" if is_main_admin(user_id) else "👑 Admin Panel"

        admin_panel_text = f"{admin_title}\n\n📊 Bot Statistics:\n👥 Total Users: {total_users}\n👑 VIP Users: {vip_users}\n\nSelect an action:"

        if is_command:
            # Send a new message with the admin panel
            update.message.reply_text(
                admin_panel_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
        else:
            # Edit the existing message with the admin panel
            query.edit_message_text(
                admin_panel_text,
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    elif not is_command and query.data == "admin_add_points":
        # Show form to add points
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "👥 Add Points to User\n\nSend a message in this format:\n\n"
            "`/add_points [user_id] [points]`\n\n"
            "Example: `/add_points 123456789 5`",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await user ID and points
        context.user_data['awaiting_add_points'] = True

    elif not is_command and query.data == "admin_remove_points":
        # Show form to remove points
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "➖ Remove Points from User\n\nSend a message in this format:\n\n"
            "`/remove_points [user_id] [points]`\n\n"
            "Example: `/remove_points 123456789 5`",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await user ID and points
        context.user_data['awaiting_remove_points'] = True

    elif not is_command and query.data == "admin_reload":
        # Reload the database and settings
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "🔄 Bot täzeden ýüklenýär...",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        try:
            # Import required modules
            import importlib
            import sys

            # Reload database connection
            db = Database()
            db.close()  # Close current connection

            # Reload configuration
            if 'config' in sys.modules:
                importlib.reload(sys.modules['config'])

            # Create new database connection
            db = Database()

            # Reset daily searches for all users
            try:
                db.cur.execute("UPDATE users SET searches_today = 0")
                db.conn.commit()
                print("Daily searches reset for all users")
            except Exception as e:
                print(f"Error resetting daily searches: {e}")

            # Set unlimited points and VIP for current admins
            for admin_id in ADMIN_IDS:
                try:
                    # Check if user exists, if not create them
                    if not db.user_exists(admin_id):
                        db.cur.execute("""
                            INSERT INTO users (user_id, username, search_points, is_vip, vip_expiry_date)
                            VALUES (?, ?, ?, ?, ?)
                        """, (admin_id, f"admin_{admin_id}", 999999999, 1, '2099-12-31'))
                        print(f"Created admin user {admin_id}")
                    else:
                        # Update existing admin
                        db.cur.execute("""
                            UPDATE users
                            SET search_points = 999999999,
                                is_vip = 1,
                                vip_expiry_date = '2099-12-31'
                            WHERE user_id = ?
                        """, (admin_id,))
                        print(f"Updated admin privileges for {admin_id}")

                    db.conn.commit()
                except Exception as e:
                    print(f"Error setting unlimited points for admin {admin_id}: {e}")

            # Close database connection
            db.close()

            # Send success message
            query.edit_message_text(
                "✅ Bot üstünlikli täzeden ýüklendi!\n\n"
                "🔄 Maglumat bazasy täzelendi\n"
                "👑 Admin hukuklary tassyklandy\n"
                "📊 Gündelik gözlegler nollandy\n"
                "⚙️ Sazlamalar täzelendi",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

        except Exception as e:
            print(f"Reload error: {e}")
            query.edit_message_text(
                f"❌ Täzeden ýüklemekde ýalňyşlyk:\n\n{str(e)}\n\nGaýtadan synanyşyň.",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )

    elif not is_command and query.data == "admin_view_stats":
        # Show stats options
        keyboard = [
            [InlineKeyboardButton("👤 User Stats", callback_data="admin_user_stats"),
             InlineKeyboardButton("📈 Global Stats", callback_data="admin_global_stats")],
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "📊 Statistics Panel\n\nSelect the type of statistics you want to view:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif not is_command and query.data == "admin_user_stats":
        # Show form to view user stats
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_view_stats")]
        ]

        query.edit_message_text(
            "� User Statistics\n\nSend a message with the user ID:\n\n"
            "`/view_stats [user_id]`\n\n"
            "Example: `/view_stats 123456789`",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await user ID
        context.user_data['awaiting_view_stats'] = True

    elif not is_command and query.data == "admin_global_stats":
        # Show global statistics
        db = Database()

        # Get various statistics
        total_users = db.get_total_users_count()
        vip_users = db.get_vip_users_count()
        daily_searches = db.get_daily_searches_count()
        weekly_searches = db.get_weekly_searches_count()
        monthly_searches = db.get_monthly_searches_count()

        # Get most searched queries
        most_searched = db.get_most_searched_queries(5)
        most_searched_text = "\n".join([f"{i+1}. {query} ({count} searches)" for i, (query, count) in enumerate(most_searched)])
        if not most_searched_text:
            most_searched_text = "No searches yet"

        # Format the statistics message
        stats_message = (
            "📊 <b>Global Statistics</b>\n\n"
            f"👥 <b>Users:</b>\n"
            f"  • Total Users: {total_users}\n"
            f"  • VIP Users: {vip_users} ({round(vip_users/total_users*100, 1)}% of total)\n\n"
            f"🔍 <b>Searches:</b>\n"
            f"  • Today: {daily_searches}\n"
            f"  • Last 7 Days: {weekly_searches}\n"
            f"  • Last 30 Days: {monthly_searches}\n\n"
            f"📱 <b>Most Searched Queries:</b>\n{most_searched_text}"
        )

        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_view_stats")]
        ]

        query.edit_message_text(
            stats_message,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif not is_command and query.data == "admin_set_vip":
        # Show form to set VIP status
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "👑 Set VIP Status\n\nSend a message in this format:\n\n"
            "`/set_vip [user_id] [1 or 0] [days]`\n\n"
            "Example: `/set_vip 123456789 1 30` to enable VIP for 30 days\n"
            "Example: `/set_vip 123456789 0` to disable VIP\n\n"
            "Note: The days parameter is optional. If not provided, VIP will be set for 30 days by default.",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await user ID and VIP status
        context.user_data['awaiting_set_vip'] = True

    elif not is_command and query.data == "admin_broadcast":
        # Show form to broadcast a message
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "📢 Broadcast Message\n\nSend a message in this format:\n\n"
            "`/broadcast [message]`\n\n"
            "Example: `/broadcast Hello everyone! New features are available.`\n\n"
            "This will send the message to all users of the bot.",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await broadcast message
        context.user_data['awaiting_broadcast'] = True

    elif not is_command and query.data == "admin_promo_codes":
        # Show promo code management options
        keyboard = [
            [InlineKeyboardButton("🎁 Create Promo Code", callback_data="admin_create_promo"),
             InlineKeyboardButton("📃 List Promo Codes", callback_data="admin_list_promos")],
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_panel")]
        ]

        query.edit_message_text(
            "🎁 Promo Code Management\n\nSelect an action:",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif not is_command and query.data == "admin_create_promo":
        # Show form to create a promo code
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_promo_codes")]
        ]

        query.edit_message_text(
            "🎁 Create Promo Code\n\nSend a message in this format:\n\n"
            "`/create_promo [CODE] [POINTS] [MAX_USES]`\n\n"
            "Example: `/create_promo SUMMER2024 10 100`\n\n"
            "This will create a promo code 'SUMMER2024' that gives 10 points and can be used 100 times.",
            parse_mode="Markdown",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

        # Set state to await promo code creation
        context.user_data['awaiting_create_promo'] = True

    elif not is_command and query.data == "admin_list_promos":
        # Show list of promo codes
        keyboard = [
            [InlineKeyboardButton("⬅️ Back", callback_data="admin_promo_codes")]
        ]

        # Get promo codes from database
        promo_codes = db.get_promo_codes()

        if not promo_codes:
            query.edit_message_text(
                "🎁 Promo Codes\n\nNo promo codes found.",
                reply_markup=InlineKeyboardMarkup(keyboard)
            )
            return

        # Format the promo codes list
        message = "📃 <b>Promo Codes List:</b>\n\n"

        for i, promo in enumerate(promo_codes, 1):
            message += f"{i}. <code>{promo['code']}</code> - {promo['points']} points\n"
            message += f"   Uses: {promo['current_uses']}/{promo['max_uses'] if promo['max_uses'] > 0 else '∞'}\n"
            message += f"   Created: {promo['created_at']}\n\n"

            # Limit message length
            if len(message) > 3000:
                message += "...and more (too many to display)"
                break

        query.edit_message_text(
            message,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(keyboard)
        )

    elif not is_command and query.data == "admin_commands":
        # Show admin commands list
        from handlers.admin_commands import admin_commands_callback
        admin_commands_callback(update, context)

def handle_admin_commands(update: Update, context: CallbackContext):
    """Handle admin commands"""
    user_id = update.effective_user.id
    message_text = update.message.text

    # Check if user is admin
    if not is_admin(user_id):
        update.message.reply_text("You are not authorized to use this command.")
        return

    # Get database connection
    db = Database()

    # Handle add points command
    if message_text.startswith('/add_points'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 3:
                update.message.reply_text("Invalid format. Use: /add_points [user_id] [points]")
                return

            target_user_id = int(parts[1])
            points = int(parts[2])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Add points to user
            db.add_search_points(target_user_id, points)

            # Get updated points
            current_points = db.get_user_search_points(target_user_id)

            # Send confirmation
            update.message.reply_text(
                f"✅ Successfully added {points} points to user {target_user_id}.\n"
                f"Current points: {current_points}"
            )

        except ValueError:
            update.message.reply_text("Invalid user ID or points value. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error adding points: {e}")

    # Handle remove points command
    elif message_text.startswith('/remove_points'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 3:
                update.message.reply_text("Invalid format. Use: /remove_points [user_id] [points]")
                return

            target_user_id = int(parts[1])
            points = int(parts[2])

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Get current points
            current_points = db.get_user_search_points(target_user_id)

            # Make sure we don't go below 0
            if current_points < points:
                points = current_points

            # Remove points from user (add negative points)
            db.add_search_points(target_user_id, -points)

            # Get updated points
            updated_points = db.get_user_search_points(target_user_id)

            # Send confirmation
            update.message.reply_text(
                f"✅ Successfully removed {points} points from user {target_user_id}.\n"
                f"Current points: {updated_points}"
            )

        except ValueError:
            update.message.reply_text("Invalid user ID or points value. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error removing points: {e}")

    # Handle reload command
    elif message_text.startswith('/reload'):
        try:
            # Check if user is admin
            if not is_admin(user_id):
                update.message.reply_text("You are not authorized to use this command.")
                return

            # Send confirmation
            update.message.reply_text("🔄 Reloading database and settings...")

            # Reload database settings
            db = Database()
            db.close()  # Close current connection
            db = Database()  # Create a new connection

            # Reset daily searches
            db.reset_daily_searches()

            # Set unlimited points for admin users
            for admin_id in ADMIN_IDS:
                if db.user_exists(admin_id):
                    try:
                        db.cur.execute("""
                            UPDATE users
                            SET search_points = 9999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999999
                            WHERE user_id = ?
                        """, (admin_id,))
                        db.conn.commit()
                    except Exception as e:
                        print(f"Error setting unlimited points for admin {admin_id}: {e}")

            # Send success message
            update.message.reply_text("✅ Database and settings reloaded successfully!")

        except Exception as e:
            update.message.reply_text(f"Error reloading database: {e}")

    # Handle view stats command
    elif message_text.startswith('/view_stats'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) != 2:
                update.message.reply_text("Invalid format. Use: /view_stats [user_id]")
                return

            try:
                target_user_id = int(parts[1])
            except ValueError:
                update.message.reply_text("Invalid user ID. Please enter a valid number.")
                return

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            try:
                # Get user info
                user_info = db.get_user_info(target_user_id)
                referrals = db.get_referral_count(target_user_id)
                total_searches = db.get_total_searches(target_user_id)

                # Format username with @ if it exists
                username = user_info.get('username', '')
                if username and not username.startswith('@'):
                    username = f"@{username}"

                # Get phone verification status
                phone_verified = db.is_phone_verified(target_user_id)
                phone_number = user_info.get('phone_number', 'Not verified')

                # Get VIP expiry date if applicable
                vip_expiry_text = "N/A"
                if user_info.get('is_vip', False) and user_info.get('vip_expiry_date'):
                    vip_expiry_text = user_info.get('vip_expiry_date')

                # Get recent searches
                recent_searches = db.get_search_history(target_user_id, 5)
                recent_searches_text = ""
                if recent_searches:
                    for i, search in enumerate(recent_searches, 1):
                        results_count = search.get('results_count', 0)
                        recent_searches_text += f"  {i}. {search['query']} ({search['search_time']}) - {results_count} results\n"
                else:
                    recent_searches_text = "  No recent searches\n"

                # Format user stats with more detailed information
                stats = (
                    f"📊 <b>User Stats for ID: {target_user_id}</b>\n\n"
                    f"👤 <b>User Information:</b>\n"
                    f"  • Username: {username}\n"
                    f"  • Full Name: {user_info.get('full_name', 'N/A')}\n"
                    f"  • Phone Number: {phone_number}\n"
                    f"  • Phone Verified: {'✅ Yes' if phone_verified else '❌ No'}\n"
                    f"  • Language: {user_info.get('language', 'tm')}\n"
                    f"  • Registration Date: {user_info.get('registration_date', 'N/A')}\n\n"

                    f"🔍 <b>Search Information:</b>\n"
                    f"  • Search Points: {user_info.get('search_points', 0)}\n"
                    f"  • Total Searches: {total_searches}\n"
                    f"  • Referrals: {referrals}\n\n"

                    f"👑 <b>VIP Status:</b>\n"
                    f"  • Status: {'✅ Active' if user_info.get('is_vip', False) else '❌ Inactive'}\n"
                    f"  • Expiry Date: {vip_expiry_text}\n\n"

                    f"🕒 <b>Recent Searches:</b>\n"
                    f"{recent_searches_text}"
                )

                # Send stats with HTML formatting
                update.message.reply_text(stats, parse_mode='HTML')
            except Exception as e:
                update.message.reply_text(f"Error getting user stats: {e}")
                print(f"Error in view_stats: {e}")

        except ValueError:
            update.message.reply_text("Invalid user ID. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error getting user stats: {e}")

    # Handle set VIP status command (permanent)
    elif message_text.startswith('/set_vip'):
        try:
            # Parse command
            parts = message_text.split()
            if len(parts) < 3 or len(parts) > 4:
                update.message.reply_text("Invalid format. Use: /set_vip [user_id] [1 or 0] [days]")
                return

            try:
                target_user_id = int(parts[1])
                vip_status = int(parts[2])

                # Get days if provided, otherwise use default (permanent)
                days = 36500  # 100 years (permanent)
                if len(parts) == 4:
                    days = int(parts[3])
                    if days <= 0:
                        update.message.reply_text("Days must be a positive number.")
                        return

                # Validate VIP status value
                if vip_status not in [0, 1]:
                    update.message.reply_text("Invalid VIP status. Use 1 for VIP, 0 for non-VIP.")
                    return
            except ValueError:
                update.message.reply_text("Invalid user ID, VIP status, or days. Please enter valid numbers.")
                return

            # Check if user exists
            if not db.user_exists(target_user_id):
                update.message.reply_text(f"User with ID {target_user_id} does not exist.")
                return

            # Set VIP status with expiry date (permanent)
            success = db.set_user_vip_status(target_user_id, vip_status == 1, days, permanent=True)

            if success:
                if vip_status == 1:
                    # Send VIP commands to the user
                    try:
                        from handlers.user_commands import vip_commands_command

                        # Create a mock update and context for the target user
                        class MockMessage:
                            def __init__(self, chat_id):
                                self.chat_id = chat_id

                            def reply_text(self, text, parse_mode=None):
                                context.bot.send_message(
                                    chat_id=self.chat_id,
                                    text=text,
                                    parse_mode=parse_mode
                                )

                        class MockUser:
                            def __init__(self, user_id):
                                self.id = user_id

                        class MockUpdate:
                            def __init__(self, message, user):
                                self.message = message
                                self.effective_user = user

                        # Create mock objects
                        mock_message = MockMessage(target_user_id)
                        mock_user = MockUser(target_user_id)
                        mock_update = MockUpdate(mock_message, mock_user)

                        # Get user's language
                        user_language = db.get_user_language(target_user_id)

                        # Add points based on VIP duration
                        if days == 30:  # 1 month VIP
                            db.add_search_points(target_user_id, 100)
                            if user_language == 'ru':
                                gift_message = "🎁 Вам подарено 100 поисковых баллов за покупку VIP на 1 месяц!"
                            elif user_language == 'en':
                                gift_message = "🎁 You have been gifted 100 search points for purchasing 1-month VIP!"
                            else:
                                gift_message = "🎁 1 aýlyk VIP satyn alanyňyz üçin size 100 gözleg baly sowgat berildi!"
                        elif days == 90:  # 3 months VIP
                            db.add_search_points(target_user_id, 300)
                            if user_language == 'ru':
                                gift_message = "🎁 Вам подарено 300 поисковых баллов за покупку VIP на 3 месяца!"
                            elif user_language == 'en':
                                gift_message = "🎁 You have been gifted 300 search points for purchasing 3-month VIP!"
                            else:
                                gift_message = "🎁 3 aýlyk VIP satyn alanyňyz üçin size 300 gözleg baly sowgat berildi!"
                        elif days == 180:  # 6 months VIP
                            db.add_search_points(target_user_id, 1000)
                            if user_language == 'ru':
                                gift_message = "🎁 Вам подарено 1000 поисковых баллов за покупку VIP на 6 месяцев!"
                            elif user_language == 'en':
                                gift_message = "🎁 You have been gifted 1000 search points for purchasing 6-month VIP!"
                            else:
                                gift_message = "🎁 6 aýlyk VIP satyn alanyňyz üçin size 1000 gözleg baly sowgat berildi!"
                        else:
                            gift_message = ""

                        # Send VIP welcome message
                        if user_language == 'ru':
                            welcome_message = f"🎉 <b>Поздравляем!</b> 🎉\n\nВам предоставлен VIP статус! Теперь у вас есть доступ к дополнительным функциям.\n\nВаш VIP статус будет действовать {days} дней.\n\nИспользуйте команду /commands, чтобы ознакомиться со списком доступных команд."
                        elif user_language == 'en':
                            welcome_message = f"🎉 <b>Congratulations!</b> 🎉\n\nYou have been given VIP status! You now have access to additional features.\n\nYour VIP status will last for {days} days.\n\nUse the /commands command to see the list of available commands."
                        else:
                            welcome_message = f"🎉 <b>Gutlaýarys!</b> 🎉\n\nSize VIP statusy berildi! Indi has köp funksiýalara eýe bolduňyz.\n\nVIP statusyňyz {days} gün dowam eder.\n\nKomandalar sanawy bilen tanyşmak üçin /commands komandasyny ulanyp bilersiňiz."

                        context.bot.send_message(
                            chat_id=target_user_id,
                            text=welcome_message,
                            parse_mode=ParseMode.HTML
                        )

                        # Send gift message if applicable
                        if gift_message:
                            context.bot.send_message(
                                chat_id=target_user_id,
                                text=gift_message,
                                parse_mode=ParseMode.HTML
                            )

                        # Send VIP commands list
                        vip_commands_command(mock_update, context)
                    except Exception as e:
                        print(f"Error sending VIP commands to user: {e}")

                    update.message.reply_text(f"✅ Successfully enabled VIP status for user {target_user_id} for {days} days.")
                else:
                    update.message.reply_text(f"✅ Successfully disabled VIP status for user {target_user_id}.")
            else:
                update.message.reply_text(f"❌ Failed to update VIP status for user {target_user_id}.")

        except ValueError:
            update.message.reply_text("Invalid user ID, VIP status, or days. Please use numbers only.")
        except Exception as e:
            update.message.reply_text(f"Error setting VIP status: {e}")

    # Handle broadcast message command
    elif message_text.startswith('/broadcast'):
        try:
            # Check if message has content
            if len(message_text) <= 11:  # '/broadcast ' is 11 characters
                update.message.reply_text("Please provide a message to broadcast.")
                return

            # Get the message content
            broadcast_message = message_text[11:].strip()

            if not broadcast_message:
                update.message.reply_text("Please provide a message to broadcast.")
                return

            # Get all users
            db = Database()
            all_users = db.get_all_user_ids()

            if not all_users:
                update.message.reply_text("No users found to broadcast to.")
                return

            # Send confirmation message
            update.message.reply_text(f"📢 Broadcasting message to {len(all_users)} users...")

            # Add signature to the message
            broadcast_message = f"{broadcast_message}\n\n📱 @tmcell993bot"

            # Send message to all users
            success_count = 0
            fail_count = 0

            for user_id in all_users:
                try:
                    context.bot.send_message(chat_id=user_id, text=broadcast_message)
                    success_count += 1
                except Exception as e:
                    print(f"Failed to send broadcast to user {user_id}: {e}")
                    fail_count += 1

            # Send completion message
            update.message.reply_text(
                f"✅ Broadcast completed!\n\n"
                f"📊 Statistics:\n"
                f"✅ Successfully sent: {success_count}\n"
                f"❌ Failed: {fail_count}"
            )

        except Exception as e:
            update.message.reply_text(f"Error broadcasting message: {e}")

    # Handle create promo code command
    elif message_text.startswith('/create_promo'):
        try:
            # Check if user is admin
            if not is_admin(user_id):
                update.message.reply_text("You are not authorized to use this command.")
                return

            # Parse command
            parts = message_text.split()
            if len(parts) != 4:
                update.message.reply_text("Invalid format. Use: /create_promo [CODE] [POINTS] [MAX_USES]")
                return

            code = parts[1].upper()

            try:
                points = int(parts[2])
                max_uses = int(parts[3])
            except ValueError:
                update.message.reply_text("Error: POINTS and MAX_USES must be numbers.")
                return

            # Create the promo code
            success, message = db.create_promo_code(code, points, max_uses, user_id)

            # Send the result
            update.message.reply_text(message)

        except Exception as e:
            update.message.reply_text(f"Error creating promo code: {e}")

    # Handle list promo codes command
    elif message_text.startswith('/list_promos'):
        try:
            # Check if user is admin
            if not is_admin(user_id):
                update.message.reply_text("You are not authorized to use this command.")
                return

            # Get all promo codes
            promo_codes = db.get_promo_codes()

            if not promo_codes:
                update.message.reply_text("No promo codes found.")
                return

            # Format the promo codes list
            message = "📃 <b>Promo Codes List:</b>\n\n"

            for i, promo in enumerate(promo_codes, 1):
                message += f"{i}. <code>{promo['code']}</code> - {promo['points']} points\n"
                message += f"   Uses: {promo['current_uses']}/{promo['max_uses'] if promo['max_uses'] > 0 else '∞'}\n"
                message += f"   Created: {promo['created_at']}\n\n"

                # Limit message length
                if len(message) > 3000:
                    message += "...and more (too many to display)"
                    break

            # Send the list
            update.message.reply_text(message, parse_mode="HTML")

        except Exception as e:
            update.message.reply_text(f"Error listing promo codes: {e}")

def set_unlimited_points_for_admin():
    """Set unlimited points for admin users"""
    db = Database()
    for admin_id in ADMIN_IDS:
        # Check if admin exists in database
        if db.user_exists(admin_id):
            try:
                # First check if search_points column exists
                db.cur.execute("PRAGMA table_info(users)")
                columns = db.cur.fetchall()
                column_names = [column[1] for column in columns]

                if 'search_points' in column_names:
                    # Set a very high number of points (effectively unlimited)
                    db.cur.execute("""
                        UPDATE users
                        SET search_points = 5
                        WHERE user_id = ?
                    """, (admin_id,))
                    db.conn.commit()
                    print(f"Set unlimited points for admin {admin_id}")
            except Exception as e:
                print(f"Error setting unlimited points for admin {admin_id}: {e}")

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script to reset old admin privileges and set new admin privileges
"""

import sqlite3
import os
import sys
from datetime import datetime

def reset_old_admins():
    """Reset old admin privileges and set new admin privileges"""
    
    # Database file path
    db_path = "user_data.db"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        # Connect to database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 Resetting old admin privileges...")
        
        # Old admin IDs that need to be reset
        old_admin_ids = [5421243466, 6404812540, 8046165511, 7440280333, 5702253746]
        
        # New admin IDs that should have unlimited privileges
        new_admin_ids = [7772025660, 7027624995, 6687750461, 5421243466]  # 5421243466 stays as admin
        
        # Reset old admins (except 5421243466 who stays)
        old_admins_to_reset = [id for id in old_admin_ids if id not in new_admin_ids]
        
        for admin_id in old_admins_to_reset:
            try:
                # Check if user exists
                cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (admin_id,))
                if cursor.fetchone():
                    # Reset their privileges to normal user
                    cursor.execute("""
                        UPDATE users 
                        SET search_points = 10,
                            is_vip = 0,
                            vip_expiry_date = NULL
                        WHERE user_id = ?
                    """, (admin_id,))
                    print(f"✅ Reset privileges for old admin: {admin_id}")
                else:
                    print(f"ℹ️ Old admin {admin_id} not found in database")
                    
            except sqlite3.Error as e:
                print(f"❌ Error resetting admin {admin_id}: {e}")
        
        # Set unlimited privileges for new admins
        for admin_id in new_admin_ids:
            try:
                # Check if user exists
                cursor.execute("SELECT user_id FROM users WHERE user_id = ?", (admin_id,))
                if cursor.fetchone():
                    # Give unlimited privileges
                    cursor.execute("""
                        UPDATE users 
                        SET search_points = 999999999,
                            is_vip = 1,
                            vip_expiry_date = '2099-12-31'
                        WHERE user_id = ?
                    """, (admin_id,))
                    print(f"✅ Set unlimited privileges for new admin: {admin_id}")
                else:
                    print(f"ℹ️ New admin {admin_id} not found in database (will get privileges when they join)")
                    
            except sqlite3.Error as e:
                print(f"❌ Error setting privileges for admin {admin_id}: {e}")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 Admin privileges updated successfully!")
        print(f"📊 Summary:")
        print(f"   - Old admins reset: {old_admins_to_reset}")
        print(f"   - New admins set: {new_admin_ids}")
        print(f"📅 Updated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Admin Privileges Reset")
    print("=" * 50)
    
    # Confirm action
    response = input("⚠️ This will reset old admin privileges. Continue? (yes/no): ").lower().strip()
    if response not in ['yes', 'y']:
        print("❌ Operation cancelled")
        sys.exit(0)
    
    reset_old_admins()
    print("\n✅ All done! Admin privileges updated.")

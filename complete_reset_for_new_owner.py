#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete reset for new owner - clean all referrals and VIPs except new admins
"""

import sqlite3
import os
import sys
from datetime import datetime
from config import ADMIN_IDS

def complete_reset_for_new_owner():
    """Complete reset for new owner"""
    
    db_path = "VL2024.sqlite"
    
    if not os.path.exists(db_path):
        print(f"Database file {db_path} not found.")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔄 COMPLETE RESET FOR NEW OWNER")
        print("=" * 60)
        print(f"New admins (to preserve): {ADMIN_IDS}")
        
        # 1. CLEAN ALL REFERRALS
        print(f"\n1️⃣ Cleaning referral system...")
        
        # Get current referral count
        cursor.execute("SELECT COUNT(*) FROM referrals")
        total_referrals = cursor.fetchone()[0]
        print(f"   Current referrals: {total_referrals}")
        
        # Delete ALL referrals (fresh start for new owner)
        cursor.execute("DELETE FROM referrals")
        deleted_referrals = cursor.rowcount
        print(f"   ✅ Deleted all referrals: {deleted_referrals}")
        
        # Reset referral-related columns in users table if they exist
        cursor.execute("PRAGMA table_info(users)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        if 'referrer_id' in column_names:
            cursor.execute("UPDATE users SET referrer_id = NULL")
            print(f"   ✅ Reset all referrer_id references: {cursor.rowcount}")
        
        # 2. CLEAN ALL VIP USERS (except new admins)
        print(f"\n2️⃣ Cleaning VIP system...")
        
        # Get current VIP count
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_vip = 1")
        total_vips = cursor.fetchone()[0]
        print(f"   Current VIP users: {total_vips}")
        
        # Show current VIPs
        cursor.execute("SELECT user_id, username, vip_expiry_date FROM users WHERE is_vip = 1")
        current_vips = cursor.fetchall()
        
        print(f"   Current VIP users:")
        vips_to_remove = []
        for user_id, username, expiry in current_vips:
            if user_id in ADMIN_IDS:
                print(f"      👑 KEEPING: {user_id} (@{username}) - New Admin")
            else:
                print(f"      ❌ REMOVING: {user_id} (@{username}) - expires {expiry}")
                vips_to_remove.append(user_id)
        
        # Remove VIP status from non-admins
        if vips_to_remove:
            placeholders = ','.join(['?' for _ in vips_to_remove])
            cursor.execute(f"""
                UPDATE users 
                SET is_vip = 0, 
                    vip_expiry_date = NULL,
                    search_points = 1
                WHERE user_id IN ({placeholders})
            """, vips_to_remove)
            
            removed_vips = cursor.rowcount
            print(f"   ✅ Removed VIP status from {removed_vips} users")
        
        # 3. RESET ALL USER POINTS (except new admins)
        print(f"\n3️⃣ Resetting all user points...")
        
        # Reset all users to 1 point except new admins
        admin_placeholders = ','.join(['?' for _ in ADMIN_IDS])
        cursor.execute(f"""
            UPDATE users 
            SET search_points = 1,
                searches_today = 0,
                total_searches = 0
            WHERE user_id NOT IN ({admin_placeholders})
        """, ADMIN_IDS)
        
        reset_users = cursor.rowcount
        print(f"   ✅ Reset {reset_users} users to 1 point")
        
        # Ensure new admins have unlimited privileges
        for admin_id in ADMIN_IDS:
            cursor.execute("""
                UPDATE users 
                SET search_points = 999999999,
                    is_vip = 1,
                    vip_expiry_date = '2099-12-31'
                WHERE user_id = ?
            """, (admin_id,))
            
            if cursor.rowcount > 0:
                print(f"   ✅ Confirmed unlimited privileges for new admin {admin_id}")
        
        # 4. CLEAN SEARCH HISTORY (optional - keep for data integrity)
        print(f"\n4️⃣ Search history status...")
        cursor.execute("SELECT COUNT(*) FROM search_history")
        search_count = cursor.fetchone()[0]
        print(f"   ℹ️ Total search history records: {search_count} (keeping for data integrity)")
        
        # 5. CLEAN PROMO CODE USAGE
        print(f"\n5️⃣ Cleaning promo code usage...")
        cursor.execute("SELECT COUNT(*) FROM promo_code_uses")
        promo_uses = cursor.fetchone()[0]
        print(f"   Current promo code uses: {promo_uses}")
        
        # Delete all promo code usage (fresh start)
        cursor.execute("DELETE FROM promo_code_uses")
        deleted_promos = cursor.rowcount
        print(f"   ✅ Deleted all promo code usage: {deleted_promos}")
        
        # 6. CLEAN FAVORITES
        print(f"\n6️⃣ Cleaning favorites...")
        cursor.execute("SELECT COUNT(*) FROM favorites")
        favorites_count = cursor.fetchone()[0]
        print(f"   Current favorites: {favorites_count}")
        
        # Delete all favorites (fresh start)
        cursor.execute("DELETE FROM favorites")
        deleted_favorites = cursor.rowcount
        print(f"   ✅ Deleted all favorites: {deleted_favorites}")
        
        # 7. CLEAN NOTIFICATIONS
        print(f"\n7️⃣ Cleaning notifications...")
        cursor.execute("SELECT COUNT(*) FROM user_notifications")
        notifications_count = cursor.fetchone()[0]
        print(f"   Current user notifications: {notifications_count}")
        
        # Delete all user notifications (fresh start)
        cursor.execute("DELETE FROM user_notifications")
        deleted_notifications = cursor.rowcount
        print(f"   ✅ Deleted all user notifications: {deleted_notifications}")
        
        # Commit all changes
        conn.commit()
        
        # 8. FINAL VERIFICATION
        print(f"\n8️⃣ Final verification...")
        
        # Check new admin status
        for admin_id in ADMIN_IDS:
            cursor.execute("SELECT username, search_points, is_vip FROM users WHERE user_id = ?", (admin_id,))
            result = cursor.fetchone()
            if result:
                username, points, is_vip = result
                status = "✅" if (points > 999999 and is_vip) else "❌"
                print(f"   {status} New admin {admin_id} (@{username}): {points} points, VIP: {is_vip}")
        
        # Check system status
        cursor.execute("SELECT COUNT(*) FROM referrals")
        final_referrals = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE is_vip = 1")
        final_vips = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM users WHERE search_points > 100 AND user_id NOT IN ({})".format(','.join(['?' for _ in ADMIN_IDS])), ADMIN_IDS)
        high_point_users = cursor.fetchone()[0]
        
        print(f"\n🎉 COMPLETE RESET FINISHED!")
        print(f"📊 Final Status:")
        print(f"   ✅ Referrals: {final_referrals} (clean slate)")
        print(f"   ✅ VIP users: {final_vips} (only new admins)")
        print(f"   ✅ High-point users: {high_point_users} (only new admins)")
        print(f"   ✅ All regular users reset to 1 point")
        print(f"   ✅ All favorites, promos, notifications cleared")
        print(f"📅 Reset completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print(f"\n🚀 BOT IS READY FOR NEW OWNER!")
        print(f"   👑 New owner can start fresh referral system")
        print(f"   💎 New owner can sell VIP to new customers")
        print(f"   📱 Phone database (3M+ records) preserved")
        print(f"   🎯 All user engagement metrics reset")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")

if __name__ == "__main__":
    print("🚀 TM CELL Bot - Complete Reset for New Owner")
    print("=" * 60)
    
    print("⚠️ WARNING: This will completely reset:")
    print("   - ALL referrals (deleted)")
    print("   - ALL VIP users (except new admins)")
    print("   - ALL user points (reset to 1)")
    print("   - ALL favorites, promos, notifications")
    print("   - Phone database will be PRESERVED")
    print("")
    
    response = input("Continue with complete reset? (yes/no): ").lower().strip()
    if response in ['yes', 'y']:
        complete_reset_for_new_owner()
    else:
        print("❌ Reset cancelled")
    
    print("\n✅ Operation complete!")

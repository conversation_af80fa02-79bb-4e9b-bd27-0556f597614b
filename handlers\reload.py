from telegram import Update
from telegram.ext import CallbackContext
from utils.db import Database
import logging

logger = logging.getLogger(__name__)

def reload_command(update: Update, context: CallbackContext):
    """Handle /reload command to reload bot settings and database"""
    user_id = update.effective_user.id

    # Check if user is admin
    from config import ADMIN_IDS
    if user_id not in ADMIN_IDS:
        update.message.reply_text("⛔ Bu komanda diňe administratorlar üçin elýeterlidir.")
        return

    # Send confirmation message
    update.message.reply_text("🔄 Bot täzeden ýüklenýär...")

    try:
        # Import required modules
        import importlib
        import sys

        # Get user's language
        db = Database()

        # Close database connections
        db.close()

        # Reload configuration
        if 'config' in sys.modules:
            importlib.reload(sys.modules['config'])
            logger.info("Configuration reloaded")

        # Create new database connection
        db = Database()

        # Reset daily searches for all users
        try:
            db.cur.execute("UPDATE users SET searches_today = 0")
            db.conn.commit()
            logger.info("Daily searches reset for all users")
        except Exception as e:
            logger.error(f"Error resetting daily searches: {e}")

        # Set unlimited points and VIP for current admins
        admin_count = 0
        for admin_id in ADMIN_IDS:
            try:
                # Check if user exists, if not create them
                if not db.user_exists(admin_id):
                    db.cur.execute("""
                        INSERT INTO users (user_id, username, search_points, is_vip, vip_expiry_date)
                        VALUES (?, ?, ?, ?, ?)
                    """, (admin_id, f"admin_{admin_id}", 999999999, 1, '2099-12-31'))
                    logger.info(f"Created admin user {admin_id}")
                else:
                    # Update existing admin
                    db.cur.execute("""
                        UPDATE users
                        SET search_points = 999999999,
                            is_vip = 1,
                            vip_expiry_date = '2099-12-31'
                        WHERE user_id = ?
                    """, (admin_id,))
                    logger.info(f"Updated admin privileges for {admin_id}")

                db.conn.commit()
                admin_count += 1
            except Exception as e:
                logger.error(f"Error setting unlimited points for admin {admin_id}: {e}")

        # Close database connection
        db.close()

        # Log the reload
        logger.info(f"Bot reload completed by user {user_id}")

        # Send success message
        context.bot.send_message(
            chat_id=user_id,
            text=f"✅ Bot üstünlikli täzeden ýüklendi!\n\n"
                 f"🔄 Maglumat bazasy täzelendi\n"
                 f"👑 {admin_count} admin hukugy tassyklandy\n"
                 f"📊 Gündelik gözlegler nollandy\n"
                 f"⚙️ Sazlamalar täzelendi"
        )

    except Exception as e:
        logger.error(f"Error during reload: {e}")
        update.message.reply_text(f"❌ Täzeden ýüklemekde ýalňyşlyk:\n\n{str(e)}")

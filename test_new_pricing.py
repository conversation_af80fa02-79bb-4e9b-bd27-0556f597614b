#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test new pricing system
"""

import sys
import os

# Add the project root to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.keyboards import get_vip_keyboard
from utils.languages import get_message

def test_new_pricing():
    """Test new pricing system"""
    
    print("🧪 Testing New Pricing System")
    print("=" * 50)
    
    # Test pricing for all languages
    languages = ['tm', 'ru', 'en']
    
    for lang in languages:
        print(f"\n📋 Language: {lang.upper()}")
        
        # Test VIP title message
        vip_title = get_message('vip_title', lang)
        print(f"VIP Title:\n{vip_title}")
        
        # Test VIP keyboard
        keyboard = get_vip_keyboard(lang)
        print(f"\nVIP Keyboard buttons:")
        for row in keyboard.inline_keyboard:
            for button in row:
                print(f"  - {button.text} (callback: {button.callback_data})")
    
    print(f"\n✅ New pricing system tested successfully!")
    print(f"\n💰 New Pricing Structure:")
    print(f"   💳 10 bal = 23 TMT ($1)")
    print(f"   💰 50 bal = 60 TMT ($3)")
    print(f"   💸 100 bal = 100 TMT ($5)")
    print(f"   🔥 200 bal = 200 TMT ($10)")
    print(f"   ⭐ Limitsiz = 750 TMT ($35)")

if __name__ == "__main__":
    test_new_pricing()

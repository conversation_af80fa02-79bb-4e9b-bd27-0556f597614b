import sqlite3
import os
from config import SQLITE_DB_PATH, ENCODE_CHARS, DECODE_CHARS
import datetime

def encode(text):
    """Encode text using the mapping defined in config"""
    if not text:
        return ""
    return ''.join(DECODE_CHARS[ENCODE_CHARS.index(c)] if c in ENCODE_CHARS else c for c in text)

def decode(text):
    """Decode text using the mapping defined in config"""
    if not text:
        return ""
    return ''.join(ENCODE_CHARS[DECODE_CHARS.index(c)] if c in DECODE_CHARS else c for c in text)



class Database:
    def __init__(self):
        if not os.path.exists(SQLITE_DB_PATH):
            raise FileNotFoundError(f"Database file {SQLITE_DB_PATH} not found")

        self.conn = sqlite3.connect(SQLITE_DB_PATH)
        self.cur = self.conn.cursor()
        self.init_tables()

        # Import extension methods
        try:
            from utils.db_extensions import (
                get_total_users_count,
                get_vip_users_count,
                get_daily_searches_count,
                get_most_searched_queries,
                get_user_search_history_stats
            )

            # Add methods to the class
            self.get_total_users_count = get_total_users_count.__get__(self)
            self.get_vip_users_count = get_vip_users_count.__get__(self)
            self.get_daily_searches_count = get_daily_searches_count.__get__(self)
            self.get_most_searched_queries = get_most_searched_queries.__get__(self)
            self.get_user_search_history_stats = get_user_search_history_stats.__get__(self)
        except ImportError as e:
            print(f"Warning: Could not import database extensions: {e}")



    def __del__(self):
        """Destructor to safely close database connection"""
        try:
            if hasattr(self, 'conn') and self.conn:
                self.conn.close()
        except Exception as e:
            print(f"Error closing database connection: {e}")

    def user_exists(self, user_id):
        """Check if a user exists in the database"""
        self.cur.execute("SELECT 1 FROM users WHERE user_id = ?", (user_id,))
        return bool(self.cur.fetchone())

    def add_user(self, user_id, username, full_name, referrer_id=None):
        """Add a new user to the database"""
        if not self.user_exists(user_id):
            from config import DEFAULT_SEARCH_POINTS
            self.cur.execute("""
                INSERT INTO users (user_id, username, full_name, referrer_id, search_points)
                VALUES (?, ?, ?, ?, ?)
            """, (user_id, username, full_name, referrer_id, DEFAULT_SEARCH_POINTS))

            # If there's a referrer, add a referral record
            if referrer_id:
                self.cur.execute("""
                    INSERT INTO referrals (referrer_id, referred_id)
                    VALUES (?, ?)
                """, (referrer_id, user_id))

                # Give the referrer an extra search point
                self.cur.execute("""
                    UPDATE users
                    SET search_points = search_points + 1
                    WHERE user_id = ?
                """, (referrer_id,))

            self.conn.commit()
            return True  # New user added
        else:
            # Update username and full_name if they've changed
            self.cur.execute("""
                UPDATE users
                SET username = ?, full_name = ?
                WHERE user_id = ?
            """, (username, full_name, user_id))
            self.conn.commit()
            return False  # Existing user updated

    def update_user_phone(self, user_id, phone_number):
        """Update user's phone number and set as verified"""
        self.cur.execute("""
            UPDATE users
            SET phone_number = ?, phone_verified = 1
            WHERE user_id = ?
        """, (phone_number, user_id))
        self.conn.commit()

    def is_phone_verified(self, user_id):
        """Check if user's phone is verified"""
        try:
            # Check if user exists and has phone_verified set to 1
            self.cur.execute("""
                SELECT phone_verified FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = self.cur.fetchone()
            return bool(result[0]) if result else False
        except Exception as e:
            print(f"Error checking phone verification: {e}")
            return False

    def save_user_phone_to_file(self, user_id, phone_number, username, full_name):
        """Save user's phone number to a file"""
        try:
            # Create directory if it doesn't exist
            import os
            if not os.path.exists('user_phones'):
                os.makedirs('user_phones')

            # Create filename based on username or user_id
            filename = f"user_phones/{username if username else user_id}.txt"

            # Write user data to file
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"User ID: {user_id}\n")
                f.write(f"Username: {username}\n")
                f.write(f"Full Name: {full_name}\n")
                f.write(f"Phone Number: {phone_number}\n")
                f.write(f"Date: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            return True
        except Exception as e:
            print(f"Error saving phone to file: {e}")
            return False

    def get_user_language(self, user_id):
        """Get user's preferred language"""
        self.cur.execute("""
            SELECT language FROM users
            WHERE user_id = ?
        """, (user_id,))
        result = self.cur.fetchone()
        return result[0] if result else 'tm'  # Default to Turkmen

    def get_user_searches_today(self, user_id):
        """Get the number of searches a user has made today"""
        today = datetime.now().date()
        self.cur.execute("""
            SELECT COUNT(*) FROM searches
            WHERE user_id = ? AND DATE(search_time) = ?
        """, (user_id, today))
        return self.cur.fetchone()[0]

    def get_user_info(self, user_id):
        """Get user information"""
        try:
            # First check if the table has the expected columns
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Build the query based on available columns
            select_columns = ["user_id"]
            if "username" in column_names:
                select_columns.append("username")
            if "full_name" in column_names:
                select_columns.append("full_name")
            if "language" in column_names:
                select_columns.append("language")
            if "is_vip" in column_names:
                select_columns.append("is_vip")
            if "vip_expiry_date" in column_names:
                select_columns.append("vip_expiry_date")
            if "searches_today" in column_names:
                select_columns.append("searches_today")
            if "search_points" in column_names:
                select_columns.append("search_points")
            if "total_searches" in column_names:
                select_columns.append("total_searches")
            if "channel_subscribed" in column_names:
                select_columns.append("channel_subscribed")
            if "referrer_id" in column_names:
                select_columns.append("referrer_id")
            if "daily_gift_received" in column_names:
                select_columns.append("daily_gift_received")
            if "is_blocked" in column_names:
                select_columns.append("is_blocked")
            if "phone_number" in column_names:
                select_columns.append("phone_number")
            if "phone_verified" in column_names:
                select_columns.append("phone_verified")

            # Execute the query
            query = f"SELECT {', '.join(select_columns)} FROM users WHERE user_id = ?"
            self.cur.execute(query, (user_id,))
            result = self.cur.fetchone()

            if result:
                # Create a dictionary with default values
                # Create a dictionary with default values
                user_info = {
                    'user_id': result[0],
                    'username': result[1] if len(result) > 1 and "username" in column_names else "",
                    'full_name': result[2] if len(result) > 2 and "full_name" in column_names else "",
                    'language': result[3] if len(result) > 3 and "language" in column_names else "tm",
                    'is_vip': bool(result[4]) if len(result) > 4 and "is_vip" in column_names else False,
                    'vip_expiry_date': result[5] if len(result) > 5 and "vip_expiry_date" in column_names else None,
                    'searches_today': result[6] if len(result) > 6 and "searches_today" in column_names else 0,
                    'search_points': result[7] if len(result) > 7 and "search_points" in column_names else 0,
                    'total_searches': result[8] if len(result) > 8 and "total_searches" in column_names else 0,
                    'channel_subscribed': bool(result[9]) if len(result) > 9 and "channel_subscribed" in column_names else False,
                    'referrer_id': result[10] if len(result) > 10 and "referrer_id" in column_names else None,
                    'daily_gift_received': bool(result[11]) if len(result) > 11 and "daily_gift_received" in column_names else False,
                    'is_blocked': bool(result[12]) if len(result) > 12 and "is_blocked" in column_names else False
                }

                # Add phone number and verification status if available
                phone_index = select_columns.index("phone_number") if "phone_number" in select_columns else -1
                if phone_index >= 0 and len(result) > phone_index:
                    user_info['phone_number'] = result[phone_index]
                else:
                    user_info['phone_number'] = ""

                phone_verified_index = select_columns.index("phone_verified") if "phone_verified" in select_columns else -1
                if phone_verified_index >= 0 and len(result) > phone_verified_index:
                    user_info['phone_verified'] = bool(result[phone_verified_index])
                else:
                    user_info['phone_verified'] = False
                return user_info
            return None
        except Exception as e:
            print(f"Error getting user info: {e}")
            # Return default values if there's an error
            return {
                'user_id': user_id,
                'username': "",
                'full_name': "",
                'language': "tm",
                'is_vip': False,
                'vip_expiry_date': None,
                'searches_today': 0,
                'search_points': 0,
                'total_searches': 0,
                'channel_subscribed': False,
                'referrer_id': None,
                'daily_gift_received': False,
                'is_blocked': False,
                'phone_number': "",
                'phone_verified': False
            }

    def get_referral_count(self, user_id):
        """Get the number of users referred by this user"""
        self.cur.execute("""
            SELECT COUNT(*) FROM referrals
            WHERE referrer_id = ?
        """, (user_id,))
        return self.cur.fetchone()[0]

    def set_user_language(self, user_id, language):
        """Set user's preferred language"""
        if self.user_exists(user_id):
            self.cur.execute("""
                UPDATE users
                SET language = ?
                WHERE user_id = ?
            """, (language, user_id))
        else:
            self.cur.execute("""
                INSERT INTO users (user_id, language)
                VALUES (?, ?)
            """, (user_id, language))
        self.conn.commit()

    def record_search(self, user_id, query, search_type='phone', results_count=0):
        """Record a search in the database and update user's search count"""
        try:
            # Record the search in searches table
            self.cur.execute("""
                INSERT INTO searches (user_id, query)
                VALUES (?, ?)
            """, (user_id, query))

            # Also record in search_history table with more details
            self.cur.execute("""
                INSERT INTO search_history (user_id, query, search_type, results_count)
                VALUES (?, ?, ?, ?)
            """, (user_id, query, search_type, results_count))

            # Check if total_searches column exists
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Update user's search counts and points based on available columns
            update_fields = []
            if "searches_today" in column_names:
                update_fields.append("searches_today = searches_today + 1")
            if "total_searches" in column_names:
                update_fields.append("total_searches = total_searches + 1")
            if "search_points" in column_names:
                # Only deduct points if user is not VIP
                user_info = self.get_user_info(user_id)
                if not user_info.get('is_vip', False):
                    update_fields.append("search_points = search_points - 1")

            if update_fields:
                query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
                self.cur.execute(query, (user_id,))

            self.conn.commit()

            # We'll skip the automatic point check after search to avoid bot parameter issues
            # This functionality will be handled elsewhere
            pass

            return True
        except Exception as e:
            print(f"Error recording search: {e}")
            return False

    def get_user_search_points(self, user_id):
        """Get the number of search points a user has"""
        try:
            # First check if user is VIP
            self.cur.execute("""
                SELECT is_vip, search_points FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = self.cur.fetchone()

            if not result:
                return 0

            is_vip, search_points = result

            # If user is VIP and has 0 or less points, add points based on VIP type
            if is_vip and search_points <= 0:
                # Get VIP expiry date to determine VIP type
                self.cur.execute("""
                    SELECT vip_expiry_date FROM users
                    WHERE user_id = ?
                """, (user_id,))
                vip_result = self.cur.fetchone()

                if vip_result and vip_result[0]:
                    # Calculate days left
                    expiry_date = datetime.datetime.strptime(vip_result[0], '%Y-%m-%d %H:%M:%S')
                    current_date = datetime.datetime.now()
                    days_left = (expiry_date - current_date).days

                    # Determine points to add based on approximate VIP duration
                    if days_left > 150:  # 6 months VIP
                        points_to_add = 600
                    elif days_left > 60:  # 3 months VIP
                        points_to_add = 300
                    else:  # 1 month VIP
                        points_to_add = 100

                    # Add points
                    self.cur.execute("""
                        UPDATE users
                        SET search_points = ?
                        WHERE user_id = ?
                    """, (points_to_add, user_id))
                    self.conn.commit()

                    return points_to_add

            return search_points
        except Exception as e:
            print(f"Error getting user search points: {e}")
            return 0

    def get_total_searches(self, user_id):
        """Get the total number of searches a user has made"""
        try:
            # First check if total_searches column exists
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            if 'total_searches' in column_names:
                # Get from users table if column exists
                self.cur.execute("""
                    SELECT total_searches FROM users
                    WHERE user_id = ?
                """, (user_id,))
                result = self.cur.fetchone()
                return result[0] if result else 0
            else:
                # Count from searches table if column doesn't exist
                self.cur.execute("""
                    SELECT COUNT(*) FROM searches
                    WHERE user_id = ?
                """, (user_id,))
                result = self.cur.fetchone()
                return result[0] if result else 0
        except Exception as e:
            print(f"Error getting total searches: {e}")
            return 0

    def has_enough_search_points(self, user_id):
        """Check if a user has enough search points"""
        points = self.get_user_search_points(user_id)
        return points > 0 or self.get_user_info(user_id).get('is_vip', False)

    def add_search_points(self, user_id, points=1, mark_as_gift=False):
        """Add search points to a user"""
        if mark_as_gift:
            self.cur.execute("""
                UPDATE users
                SET search_points = search_points + ?, daily_gift_received = 1
                WHERE user_id = ?
            """, (points, user_id))
        else:
            self.cur.execute("""
                UPDATE users
                SET search_points = search_points + ?
                WHERE user_id = ?
            """, (points, user_id))
        self.conn.commit()
        return True

    # Channel subscription functionality has been removed

    def reset_daily_searches(self):
        """Reset daily search counts and daily gift status for all users (to be called once per day)"""
        # First check if daily_gift_received column exists
        self.cur.execute("PRAGMA table_info(users)")
        columns = self.cur.fetchall()
        column_names = [column[1] for column in columns]

        # If daily_gift_received column exists, reset it along with searches_today
        if 'daily_gift_received' in column_names:
            self.cur.execute("""
                UPDATE users
                SET searches_today = 0, daily_gift_received = 0
            """)
        else:
            # Otherwise just reset searches_today
            self.cur.execute("""
                UPDATE users
                SET searches_today = 0
            """)

            # Add daily_gift_received column if it doesn't exist
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN daily_gift_received BOOLEAN DEFAULT 0")
                print("Added daily_gift_received column to users table")
            except Exception as e:
                print(f"Error adding daily_gift_received column: {e}")

        self.conn.commit()

    def search_phone_number(self, query, search_type='phone'):
        """Search for data using the file-based search

        Args:
            query (str): The search query
            search_type (str): Type of search - 'phone', 'name', or 'passport'

        Returns:
            list: List of matching results
        """
        # Import the file search module
        import file_search

        # Use the file-based search function with the specified search type
        results = file_search.search_phone_data(query, search_type)

        # Return the results from the file-based search
        return results

    def _search_phone_number_in_db(self, phone_number):
        """Search for a phone number directly in the database (fallback method)"""
        results = []

        # Clean and standardize the phone number
        phone_number = phone_number.replace('+', '').replace(' ', '').replace('-', '').strip()

        # Check if the phone number is valid
        if not phone_number.isdigit():
            return results

        # Prepare possible number formats
        possible_numbers = [encode(phone_number)]

        # Add variations with and without country code
        if phone_number.startswith("993") and len(phone_number) >= 12:
            possible_numbers.append(encode(phone_number[3:]))  # Without 993
        elif len(phone_number) >= 8 and len(phone_number) <= 9:
            possible_numbers.append(encode("993" + phone_number))  # With 993

        # For partial matches (last 7 digits)
        if len(phone_number) >= 7:
            possible_numbers.append(encode(phone_number[-7:]))

        try:
            for encrypted_number in possible_numbers:
                # First try exact match in Tel1 table
                self.cur.execute("""
                    SELECT Col001, Col002, Col003, Col004, Col005, Col006
                    FROM Tel1
                    WHERE Col001 = ?
                    LIMIT 5
                """, (encrypted_number,))

                tel1_results = self.cur.fetchall()
                for row in tel1_results:
                    result = {
                        'phone_number': decode(row[0]),
                        'full_name': decode(row[1]),
                        'address': decode(row[2]),
                        'passport': decode(row[3]),
                        'birth_info': decode(row[4]),
                        'sim_id': decode(row[5])
                    }
                    # Only add if not already in results
                    if not any(r.get('phone_number') == result['phone_number'] for r in results):
                        results.append(result)

                # If we didn't find results, try with LIKE
                if not tel1_results:
                    self.cur.execute("""
                        SELECT Col001, Col002, Col003, Col004, Col005, Col006
                        FROM Tel1
                        WHERE Col001 LIKE ?
                        LIMIT 5
                    """, (f"%{encrypted_number}%",))

                    tel1_results = self.cur.fetchall()
                    for row in tel1_results:
                        result = {
                            'phone_number': decode(row[0]),
                            'full_name': decode(row[1]),
                            'address': decode(row[2]),
                            'passport': decode(row[3]),
                            'birth_info': decode(row[4]),
                            'sim_id': decode(row[5])
                        }
                        # Only add if not already in results
                        if not any(r.get('phone_number') == result['phone_number'] for r in results):
                            results.append(result)

                # Search in Tel2 table
                self.cur.execute("""
                    SELECT Col001, Col002, Col003, Col004, Col005, Col006
                    FROM Tel2
                    WHERE Col001 = ?
                    LIMIT 5
                """, (encrypted_number,))

                tel2_results = self.cur.fetchall()
                for row in tel2_results:
                    result = {
                        'phone_number': decode(row[0]),
                        'full_name': decode(row[1]),
                        'address': decode(row[2]),
                        'passport': decode(row[3]),
                        'birth_info': decode(row[4]),
                        'sim_id': decode(row[5])
                    }
                    # Only add if not already in results
                    if not any(r.get('phone_number') == result['phone_number'] for r in results):
                        results.append(result)

                # If we didn't find results, try with LIKE
                if not tel2_results:
                    self.cur.execute("""
                        SELECT Col001, Col002, Col003, Col004, Col005, Col006
                        FROM Tel2
                        WHERE Col001 LIKE ?
                        LIMIT 5
                    """, (f"%{encrypted_number}%",))

                    tel2_results = self.cur.fetchall()
                    for row in tel2_results:
                        result = {
                            'phone_number': decode(row[0]),
                            'full_name': decode(row[1]),
                            'address': decode(row[2]),
                            'passport': decode(row[3]),
                            'birth_info': decode(row[4]),
                            'sim_id': decode(row[5])
                        }
                        # Only add if not already in results
                        if not any(r.get('phone_number') == result['phone_number'] for r in results):
                            results.append(result)

                # If we found enough results, no need to try other formats
                if len(results) >= 3:
                    break

        except Exception as e:
            print(f"Error searching for phone number in database: {e}")

        return results

    def add_to_favorites(self, user_id, result):
        """Add a search result to user's favorites"""
        try:
            # Check if this result is already in favorites
            self.cur.execute("""
                SELECT 1 FROM favorites
                WHERE user_id = ? AND phone_number = ?
            """, (user_id, result.get('phone_number', '')))

            if self.cur.fetchone():
                # Already in favorites
                return False

            # Add to favorites
            self.cur.execute("""
                INSERT INTO favorites (
                    user_id, phone_number, full_name, address,
                    passport, birth_info, sim_id
                ) VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                user_id,
                result.get('phone_number', ''),
                result.get('full_name', ''),
                result.get('address', ''),
                result.get('passport', ''),
                result.get('birth_info', ''),
                result.get('sim_id', '')
            ))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error adding to favorites: {e}")
            return False

    def get_user_favorites(self, user_id, limit=20):
        """Get user's favorite search results"""
        try:
            self.cur.execute("""
                SELECT phone_number, full_name, address, passport, birth_info, sim_id, saved_date
                FROM favorites
                WHERE user_id = ?
                ORDER BY saved_date DESC
                LIMIT ?
            """, (user_id, limit))

            results = self.cur.fetchall()
            favorites = []

            for row in results:
                favorites.append({
                    'phone_number': row[0],
                    'full_name': row[1],
                    'address': row[2],
                    'passport': row[3],
                    'birth_info': row[4],
                    'sim_id': row[5],
                    'saved_date': row[6]
                })

            return favorites
        except Exception as e:
            print(f"Error getting favorites: {e}")
            return []

    def delete_favorite(self, user_id, favorite):
        """Delete a favorite from the user's favorites"""
        try:
            # Get the phone number from the favorite
            phone_number = favorite.get('phone_number', '')

            if not phone_number:
                return False

            self.cur.execute("""
                DELETE FROM favorites
                WHERE user_id = ? AND phone_number = ?
            """, (user_id, phone_number))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting favorite: {e}")
            return False

    def clear_search_history(self, user_id):
        """Clear all search history for a user"""
        try:
            self.cur.execute("""
                DELETE FROM search_history
                WHERE user_id = ?
            """, (user_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error clearing search history: {e}")
            return False

    def set_user_theme(self, user_id, theme):
        """Set user's theme preference"""
        try:
            # Check if theme column exists, if not add it
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            if 'theme' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN theme TEXT DEFAULT 'light'")

            # Update user's theme
            self.cur.execute("""
                UPDATE users
                SET theme = ?
                WHERE user_id = ?
            """, (theme, user_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error setting user theme: {e}")
            return False

    def get_search_history(self, user_id, limit=20):
        """Get user's search history"""
        try:
            self.cur.execute("""
                SELECT query, search_type, results_count, search_time
                FROM search_history
                WHERE user_id = ?
                ORDER BY search_time DESC
                LIMIT ?
            """, (user_id, limit))

            results = self.cur.fetchall()
            history = []

            for row in results:
                history.append({
                    'query': row[0],
                    'search_type': row[1],
                    'results_count': row[2],
                    'search_time': row[3]
                })

            return history
        except Exception as e:
            print(f"Error getting search history: {e}")
            return []

    def create_promo_code(self, code, points, max_uses, created_by, expiry_days=None, promo_type='points', vip_days=0):
        """Create a new promo code with advanced options"""
        try:
            # Check if code already exists
            self.cur.execute("SELECT 1 FROM promo_codes WHERE code = ?", (code,))
            if self.cur.fetchone():
                return False, "Promo code already exists"

            # Calculate expiry date if provided
            expiry_date = None
            if expiry_days:
                expiry_date = (datetime.datetime.now() + datetime.timedelta(days=expiry_days)).strftime('%Y-%m-%d %H:%M:%S')

            # Create new promo code with advanced options
            self.cur.execute("""
                INSERT INTO promo_codes (code, points, max_uses, created_by, expiry_date, promo_type, vip_days)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (code, points, max_uses, created_by, expiry_date, promo_type, vip_days))

            self.conn.commit()

            # Format success message based on promo type
            if promo_type == 'points':
                return True, f"Promo code created successfully: {code} ({points} points)"
            elif promo_type == 'vip':
                return True, f"VIP promo code created successfully: {code} ({vip_days} days)"
            else:
                return True, f"Combined promo code created successfully: {code} ({points} points, {vip_days} VIP days)"
        except Exception as e:
            print(f"Error creating promo code: {e}")
            return False, f"Error: {e}"

    def use_promo_code(self, user_id, code):
        """Use a promo code to get points or VIP status"""
        try:
            # Check if code exists
            self.cur.execute("""
                SELECT id, points, max_uses, current_uses, expiry_date, promo_type, vip_days
                FROM promo_codes
                WHERE code = ?
            """, (code,))
            promo = self.cur.fetchone()

            if not promo:
                return False, "Invalid promo code"

            promo_id, points, max_uses, current_uses, expiry_date, promo_type, vip_days = promo

            # Check if code has expired
            if expiry_date:
                current_time = datetime.datetime.now()
                expiry_time = datetime.datetime.strptime(expiry_date, '%Y-%m-%d %H:%M:%S')
                if current_time > expiry_time:
                    return False, "This promo code has expired"

            # Check if code has reached max uses
            if max_uses > 0 and current_uses >= max_uses:
                return False, "Promo code has reached maximum uses"

            # Check if user has already used this code
            self.cur.execute("""
                SELECT 1 FROM promo_code_uses
                WHERE user_id = ? AND promo_code_id = ?
            """, (user_id, promo_id))

            if self.cur.fetchone():
                return False, "You have already used this promo code"

            # Record promo code use
            self.cur.execute("""
                INSERT INTO promo_code_uses (user_id, promo_code_id)
                VALUES (?, ?)
            """, (user_id, promo_id))

            # Update promo code uses count
            self.cur.execute("""
                UPDATE promo_codes
                SET current_uses = current_uses + 1
                WHERE id = ?
            """, (promo_id,))

            # Apply benefits based on promo type
            success_message = ""

            if promo_type == 'points' or promo_type == 'combined':
                # Add points to user
                self.cur.execute("""
                    UPDATE users
                    SET search_points = search_points + ?
                    WHERE user_id = ?
                """, (points, user_id))
                success_message += f"You received {points} search points."

            if promo_type == 'vip' or promo_type == 'combined':
                # Set VIP status for the specified days
                if vip_days > 0:
                    # Get current VIP status and expiry date
                    self.cur.execute("""
                        SELECT is_vip, vip_expiry_date FROM users
                        WHERE user_id = ?
                    """, (user_id,))
                    current_vip = self.cur.fetchone()

                    if current_vip:
                        is_vip, current_expiry = current_vip

                        # Calculate new expiry date
                        if is_vip and current_expiry:
                            # If already VIP, extend the expiry date
                            try:
                                current_expiry_date = datetime.datetime.strptime(current_expiry, '%Y-%m-%d %H:%M:%S')
                                new_expiry_date = (current_expiry_date + datetime.timedelta(days=vip_days)).strftime('%Y-%m-%d %H:%M:%S')
                            except:
                                # If there's an error parsing the date, just set a new one
                                new_expiry_date = (datetime.datetime.now() + datetime.timedelta(days=vip_days)).strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            # If not VIP, set new expiry date from now
                            new_expiry_date = (datetime.datetime.now() + datetime.timedelta(days=vip_days)).strftime('%Y-%m-%d %H:%M:%S')

                        # Update VIP status
                        self.cur.execute("""
                            UPDATE users
                            SET is_vip = 1, vip_expiry_date = ?
                            WHERE user_id = ?
                        """, (new_expiry_date, user_id))

                        if success_message:
                            success_message += f" And you got VIP status for {vip_days} days."
                        else:
                            success_message = f"You got VIP status for {vip_days} days."

            self.conn.commit()
            return True, f"Success! {success_message}"
        except Exception as e:
            print(f"Error using promo code: {e}")
            return False, f"Error: {e}"

    def get_promo_codes(self):
        """Get all promo codes with advanced details"""
        try:
            self.cur.execute("""
                SELECT code, points, max_uses, current_uses, created_at, expiry_date, promo_type, vip_days
                FROM promo_codes
                ORDER BY created_at DESC
            """)

            codes = self.cur.fetchall()
            result = []

            for code in codes:
                # Check if code has expired
                is_expired = False
                if code[5]:  # expiry_date
                    try:
                        current_time = datetime.datetime.now()
                        expiry_time = datetime.datetime.strptime(code[5], '%Y-%m-%d %H:%M:%S')
                        is_expired = current_time > expiry_time
                    except:
                        pass

                result.append({
                    'code': code[0],
                    'points': code[1],
                    'max_uses': code[2],
                    'current_uses': code[3],
                    'created_at': code[4],
                    'expiry_date': code[5],
                    'is_expired': is_expired,
                    'promo_type': code[6],
                    'vip_days': code[7]
                })

            return result
        except Exception as e:
            print(f"Error getting promo codes: {e}")
            return []

    def get_total_users_count(self):
        """Get the total number of users in the database"""
        try:
            self.cur.execute("SELECT COUNT(*) FROM users")
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting total users count: {e}")
            return 0

    def get_all_user_ids(self):
        """Get all user IDs from the database"""
        try:
            self.cur.execute("SELECT user_id FROM users")
            results = self.cur.fetchall()
            return [result[0] for result in results] if results else []
        except Exception as e:
            print(f"Error getting all user IDs: {e}")
            return []

    def get_all_users_with_points(self):
        """Get all users with their points and VIP status"""
        try:
            self.cur.execute("""
                SELECT user_id, is_vip, search_points, language FROM users
            """)
            results = self.cur.fetchall()
            users = []
            for row in results:
                users.append({
                    'user_id': row[0],
                    'is_vip': bool(row[1]),
                    'search_points': row[2],
                    'language': row[3]
                })
            return users
        except Exception as e:
            print(f"Error getting all users with points: {e}")
            return []

    def get_vip_users_count(self):
        """Get the number of VIP users in the database"""
        try:
            self.cur.execute("SELECT COUNT(*) FROM users WHERE is_vip = 1")
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting VIP users count: {e}")
            return 0



    def get_daily_searches_count(self):
        """Get the number of searches made today"""
        try:
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            self.cur.execute("""
                SELECT COUNT(*) FROM searches
                WHERE DATE(search_time) = ?
            """, (today,))
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting daily searches count: {e}")
            return 0

    def get_weekly_searches_count(self):
        """Get the number of searches made in the last 7 days"""
        try:
            # Get date from 7 days ago
            seven_days_ago = (datetime.datetime.now() - datetime.timedelta(days=7)).strftime('%Y-%m-%d')
            self.cur.execute("""
                SELECT COUNT(*) FROM searches
                WHERE DATE(search_time) >= ?
            """, (seven_days_ago,))
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting weekly searches count: {e}")
            return 0

    def get_monthly_searches_count(self):
        """Get the number of searches made in the last 30 days"""
        try:
            # Get date from 30 days ago
            thirty_days_ago = (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d')
            self.cur.execute("""
                SELECT COUNT(*) FROM searches
                WHERE DATE(search_time) >= ?
            """, (thirty_days_ago,))
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting monthly searches count: {e}")
            return 0

    def get_most_searched_queries(self, limit=5):
        """Get the most frequently searched queries"""
        try:
            self.cur.execute("""
                SELECT query, COUNT(*) as search_count
                FROM searches
                GROUP BY query
                ORDER BY search_count DESC
                LIMIT ?
            """, (limit,))
            results = self.cur.fetchall()
            return [(row[0], row[1]) for row in results] if results else []
        except Exception as e:
            print(f"Error getting most searched queries: {e}")
            return []

    def get_top_users_by_points(self, limit=100):
        """Get top users by search points"""
        try:
            self.cur.execute("""
                SELECT user_id, username, full_name, search_points
                FROM users
                ORDER BY search_points DESC
                LIMIT ?
            """, (limit,))
            results = self.cur.fetchall()
            top_users = []
            for row in results:
                username = row[1] if row[1] else "Bilinmeýän"
                if username and not username.startswith('@'):
                    username = f"@{username}"
                top_users.append({
                    'user_id': row[0],
                    'username': username,
                    'full_name': row[2],
                    'points': row[3]
                })
            return top_users
        except Exception as e:
            print(f"Error getting top users by points: {e}")
            return []

    def get_top_users_by_referrals(self, limit=100):
        """Get top users by referral count"""
        try:
            self.cur.execute("""
                SELECT r.referrer_id, u.username, u.full_name, COUNT(*) as referral_count
                FROM referrals r
                JOIN users u ON r.referrer_id = u.user_id
                GROUP BY r.referrer_id
                ORDER BY referral_count DESC
                LIMIT ?
            """, (limit,))
            results = self.cur.fetchall()
            top_referrers = []
            for row in results:
                username = row[1] if row[1] else "Bilinmeýän"
                if username and not username.startswith('@'):
                    username = f"@{username}"
                top_referrers.append({
                    'user_id': row[0],
                    'username': username,
                    'full_name': row[2],
                    'referrals': row[3]
                })
            return top_referrers
        except Exception as e:
            print(f"Error getting top users by referrals: {e}")
            return []

    def set_user_vip_status(self, user_id, is_vip=True, days=30, permanent=False):
        """Set a user's VIP status with expiry date"""
        try:
            # Check if user already has VIP
            self.cur.execute("""
                SELECT is_vip, vip_expiry_date FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = self.cur.fetchone()

            # Check if vip_status_changed_at column exists
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Add vip_status_changed_at column if it doesn't exist
            if 'vip_status_changed_at' not in column_names:
                try:
                    # SQLite doesn't support adding columns with non-constant defaults
                    # So we add the column without a default, then update it
                    self.cur.execute("ALTER TABLE users ADD COLUMN vip_status_changed_at TIMESTAMP")
                    # Update existing rows with current timestamp for VIP users
                    self.cur.execute("UPDATE users SET vip_status_changed_at = CURRENT_TIMESTAMP WHERE is_vip = 1 AND vip_status_changed_at IS NULL")
                    print("Added vip_status_changed_at column to users table")
                    self.conn.commit()
                except Exception as e:
                    print(f"Error adding vip_status_changed_at column: {e}")

            # Calculate expiry date if VIP is being enabled
            if is_vip:
                # If permanent VIP, set expiry date to a very far future date (100 years)
                if permanent:
                    expiry_date = (datetime.datetime.now() + datetime.timedelta(days=36500)).strftime('%Y-%m-%d %H:%M:%S')
                else:
                    # Set expiry date to X days from now
                    current_time = datetime.datetime.now()

                    # Make sure days is an integer
                    try:
                        days = int(days)
                    except:
                        days = 30  # Default to 30 days if conversion fails

                    # If user already has VIP, extend the expiry date
                    if result and result[0] == 1 and result[1]:
                        try:
                            current_expiry = datetime.datetime.strptime(result[1], '%Y-%m-%d %H:%M:%S')
                            # Only extend if current expiry is in the future
                            if current_expiry > current_time:
                                expiry_date = (current_expiry + datetime.timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                expiry_date = (current_time + datetime.timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
                        except:
                            # If there's an error parsing the date, just set a new one
                            expiry_date = (current_time + datetime.timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
                    else:
                        expiry_date = (current_time + datetime.timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')

                # Add points based on VIP duration
                if days == 30:  # 1 month VIP
                    self.add_search_points(user_id, 100)
                elif days == 90:  # 3 months VIP
                    self.add_search_points(user_id, 300)
                elif days == 180:  # 6 months VIP
                    self.add_search_points(user_id, 1000)

                # Current timestamp for vip_status_changed_at
                current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

                self.cur.execute("""
                    UPDATE users
                    SET is_vip = ?, vip_expiry_date = ?, vip_status_changed_at = ?
                    WHERE user_id = ?
                """, (1, expiry_date, current_timestamp, user_id))
            else:
                # If disabling VIP, set expiry date to NULL and reset points to 1
                self.cur.execute("""
                    UPDATE users
                    SET is_vip = ?, vip_expiry_date = NULL, search_points = 1
                    WHERE user_id = ?
                """, (0, user_id))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error setting VIP status: {e}")
            return False

    def get_vip_expiry_date(self, user_id):
        """Get a user's VIP expiry date with detailed information"""
        try:
            self.cur.execute("""
                SELECT vip_expiry_date FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = self.cur.fetchone()

            if result and result[0]:
                try:
                    # Parse the expiry date
                    expiry_date = datetime.datetime.strptime(result[0], '%Y-%m-%d %H:%M:%S')
                    current_date = datetime.datetime.now()

                    # Calculate time remaining
                    if expiry_date > current_date:
                        time_diff = expiry_date - current_date
                        days_left = time_diff.days
                        hours_left = time_diff.seconds // 3600
                        minutes_left = (time_diff.seconds % 3600) // 60
                        seconds_left = time_diff.seconds % 60

                        return {
                            'expiry_date': result[0],
                            'days_left': days_left,
                            'hours_left': hours_left,
                            'minutes_left': minutes_left,
                            'seconds_left': seconds_left,
                            'expired': False
                        }
                    else:
                        return {
                            'expiry_date': result[0],
                            'days_left': 0,
                            'hours_left': 0,
                            'minutes_left': 0,
                            'seconds_left': 0,
                            'expired': True
                        }
                except Exception as e:
                    print(f"Error parsing VIP expiry date: {e}")
                    return {'expiry_date': result[0], 'expired': False}

            return None
        except Exception as e:
            print(f"Error getting VIP expiry date: {e}")
            return None

    def check_vip_status(self):
        """Check and update expired VIP statuses"""
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Get users whose VIP status is about to expire
            self.cur.execute("""
                SELECT user_id FROM users
                WHERE is_vip = 1 AND vip_expiry_date IS NOT NULL AND vip_expiry_date < ?
            """, (current_time,))

            expired_users = [row[0] for row in self.cur.fetchall()]

            # Reset VIP users' points to 1 when their VIP expires
            for user_id in expired_users:
                self.cur.execute("""
                    UPDATE users
                    SET search_points = 1
                    WHERE user_id = ?
                """, (user_id,))

            # Update VIP status
            self.cur.execute("""
                UPDATE users
                SET is_vip = 0
                WHERE is_vip = 1 AND vip_expiry_date IS NOT NULL AND vip_expiry_date < ?
            """, (current_time,))

            self.conn.commit()
            return len(expired_users)  # Return number of updated users
        except Exception as e:
            print(f"Error checking VIP status: {e}")
            return 0

    # Notification system functions
    def create_notification(self, title, message, notification_type, created_by, expires_days=None):
        """Create a new notification"""
        try:
            # Calculate expiry date if provided
            expires_at = None
            if expires_days:
                expires_at = (datetime.datetime.now() + datetime.timedelta(days=expires_days)).strftime('%Y-%m-%d %H:%M:%S')

            # Insert notification
            self.cur.execute("""
                INSERT INTO notifications (title, message, notification_type, created_by, expires_at)
                VALUES (?, ?, ?, ?, ?)
            """, (title, message, notification_type, created_by, expires_at))

            self.conn.commit()
            notification_id = self.cur.lastrowid

            return True, notification_id
        except Exception as e:
            print(f"Error creating notification: {e}")
            return False, None

    def get_active_notifications(self):
        """Get all active notifications"""
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.cur.execute("""
                SELECT id, title, message, notification_type, created_at, expires_at, created_by
                FROM notifications
                WHERE is_active = 1 AND (expires_at IS NULL OR expires_at > ?)
                ORDER BY created_at DESC
            """, (current_time,))

            notifications = self.cur.fetchall()
            result = []

            for notification in notifications:
                result.append({
                    'id': notification[0],
                    'title': notification[1],
                    'message': notification[2],
                    'notification_type': notification[3],
                    'created_at': notification[4],
                    'expires_at': notification[5],
                    'created_by': notification[6]
                })

            return result
        except Exception as e:
            print(f"Error getting active notifications: {e}")
            return []

    def get_user_notifications(self, user_id):
        """Get notifications for a specific user"""
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Get all active notifications
            self.cur.execute("""
                SELECT n.id, n.title, n.message, n.notification_type, n.created_at, n.expires_at,
                       CASE WHEN un.is_read IS NULL THEN 0 ELSE un.is_read END as is_read
                FROM notifications n
                LEFT JOIN user_notifications un ON n.id = un.notification_id AND un.user_id = ?
                WHERE n.is_active = 1 AND (n.expires_at IS NULL OR n.expires_at > ?)
                ORDER BY n.created_at DESC
            """, (user_id, current_time))

            notifications = self.cur.fetchall()
            result = []

            for notification in notifications:
                result.append({
                    'id': notification[0],
                    'title': notification[1],
                    'message': notification[2],
                    'notification_type': notification[3],
                    'created_at': notification[4],
                    'expires_at': notification[5],
                    'is_read': bool(notification[6])
                })

            return result
        except Exception as e:
            print(f"Error getting user notifications: {e}")
            return []

    def mark_notification_as_read(self, user_id, notification_id):
        """Mark a notification as read for a specific user"""
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            # Check if entry exists
            self.cur.execute("""
                SELECT 1 FROM user_notifications
                WHERE user_id = ? AND notification_id = ?
            """, (user_id, notification_id))

            if self.cur.fetchone():
                # Update existing entry
                self.cur.execute("""
                    UPDATE user_notifications
                    SET is_read = 1, read_at = ?
                    WHERE user_id = ? AND notification_id = ?
                """, (current_time, user_id, notification_id))
            else:
                # Create new entry
                self.cur.execute("""
                    INSERT INTO user_notifications (user_id, notification_id, is_read, read_at)
                    VALUES (?, ?, 1, ?)
                """, (user_id, notification_id, current_time))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error marking notification as read: {e}")
            return False

    def get_unread_notifications_count(self, user_id):
        """Get the count of unread notifications for a user"""
        try:
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            self.cur.execute("""
                SELECT COUNT(*) FROM notifications n
                LEFT JOIN user_notifications un ON n.id = un.notification_id AND un.user_id = ?
                WHERE n.is_active = 1
                AND (n.expires_at IS NULL OR n.expires_at > ?)
                AND (un.is_read IS NULL OR un.is_read = 0)
            """, (user_id, current_time))

            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting unread notifications count: {e}")
            return 0

    def delete_notification(self, notification_id):
        """Delete a notification (mark as inactive)"""
        try:
            self.cur.execute("""
                UPDATE notifications
                SET is_active = 0
                WHERE id = ?
            """, (notification_id,))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error deleting notification: {e}")
            return False

    def set_user_notification_preference(self, user_id, enabled):
        """Set user's notification preference"""
        try:
            # Check if notifications_enabled column exists, if not add it
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            if 'notifications_enabled' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN notifications_enabled BOOLEAN DEFAULT 1")

            # Update user's notification preference
            self.cur.execute("""
                UPDATE users
                SET notifications_enabled = ?
                WHERE user_id = ?
            """, (1 if enabled else 0, user_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error setting user notification preference: {e}")
            return False

    def close(self):
        """Close the database connection"""
        try:
            if hasattr(self, 'conn') and self.conn and self.conn.in_transaction:
                self.conn.commit()  # Commit any pending transactions

            if hasattr(self, 'cur') and self.cur:
                try:
                    self.cur.close()
                except Exception:
                    pass  # Ignore errors when closing cursor

            if hasattr(self, 'conn') and self.conn:
                try:
                    self.conn.close()
                except Exception:
                    pass  # Ignore errors when closing connection

            return True
        except Exception as e:
            print(f"Error closing database connection: {e}")
            return False

    def block_user(self, user_id):
        """Block a user from using the bot"""
        try:
            self.cur.execute("""
                UPDATE users
                SET is_blocked = 1
                WHERE user_id = ?
            """, (user_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error blocking user: {e}")
            return False

    def unblock_user(self, user_id):
        """Unblock a user to allow them to use the bot again"""
        try:
            self.cur.execute("""
                UPDATE users
                SET is_blocked = 0
                WHERE user_id = ?
            """, (user_id,))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error unblocking user: {e}")
            return False

    def is_user_blocked(self, user_id):
        """Check if a user is blocked"""
        try:
            self.cur.execute("""
                SELECT is_blocked FROM users
                WHERE user_id = ?
            """, (user_id,))
            result = self.cur.fetchone()
            return bool(result[0]) if result else False
        except Exception as e:
            print(f"Error checking if user is blocked: {e}")
            return False

    def __del__(self):
        """Close the database connection when the object is destroyed"""
        self.close()

    def update_user_profile(self, user_id, email=None, phone=None, full_name=None, bio=None, profile_photo=None):
        """Update user's profile information"""
        try:
            # Check if columns exist, if not add them
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Add email column if it doesn't exist
            if 'email' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN email TEXT")

            # Add bio column if it doesn't exist
            if 'bio' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN bio TEXT")

            # Add profile_photo column if it doesn't exist
            if 'profile_photo' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN profile_photo TEXT")

            # Add theme column if it doesn't exist
            if 'theme' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN theme TEXT DEFAULT 'light'")

            # Build update query based on provided parameters
            update_fields = []
            params = []

            if email is not None:
                update_fields.append("email = ?")
                params.append(email)

            if phone is not None:
                update_fields.append("phone_number = ?")
                params.append(phone)

            if full_name is not None:
                update_fields.append("full_name = ?")
                params.append(full_name)

            if bio is not None:
                update_fields.append("bio = ?")
                params.append(bio)

            if profile_photo is not None:
                update_fields.append("profile_photo = ?")
                params.append(profile_photo)

            if update_fields:
                # Add user_id to params
                params.append(user_id)

                # Execute update query
                query = f"UPDATE users SET {', '.join(update_fields)} WHERE user_id = ?"
                self.cur.execute(query, params)
                self.conn.commit()

                return True

            return False
        except Exception as e:
            print(f"Error updating user profile: {e}")
            return False

    def set_user_theme(self, user_id, theme):
        """Set user's theme preference (light, dark, or auto)"""
        try:
            # Check if theme column exists, if not add it
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            if 'theme' not in column_names:
                self.cur.execute("ALTER TABLE users ADD COLUMN theme TEXT DEFAULT 'light'")

            # Update user's theme preference
            self.cur.execute("""
                UPDATE users
                SET theme = ?
                WHERE user_id = ?
            """, (theme, user_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error setting user theme: {e}")
            return False

    def set_user_auto_theme_preference(self, user_id, auto_theme):
        """Set user's auto theme preference"""
        try:
            # First check if auto_theme column exists
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Add auto_theme column if it doesn't exist
            if 'auto_theme' not in column_names:
                try:
                    self.cur.execute("ALTER TABLE users ADD COLUMN auto_theme TEXT DEFAULT 'light'")
                    print("Added auto_theme column to users table")
                except Exception as e:
                    print(f"Error adding auto_theme column: {e}")

            # Update the auto_theme preference
            self.cur.execute("""
                UPDATE users
                SET auto_theme = ?
                WHERE user_id = ?
            """, (auto_theme, user_id))
            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error setting auto theme preference: {e}")
            return False

    def save_search_preferences(self, user_id, results_per_page=None, sort_order=None, default_search_type=None):
        """Save user's search preferences"""
        try:
            # Check if search_preferences table exists, if not create it
            self.cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='search_preferences'")
            if not self.cur.fetchone():
                self.cur.execute("""
                CREATE TABLE IF NOT EXISTS search_preferences (
                    user_id INTEGER PRIMARY KEY,
                    results_per_page INTEGER DEFAULT 5,
                    sort_order TEXT DEFAULT 'desc',
                    default_search_type TEXT DEFAULT 'phone',
                    FOREIGN KEY (user_id) REFERENCES users (user_id)
                )
                """)

            # Check if user already has preferences
            self.cur.execute("SELECT 1 FROM search_preferences WHERE user_id = ?", (user_id,))
            user_has_preferences = bool(self.cur.fetchone())

            # Build query based on provided parameters
            if user_has_preferences:
                update_fields = []
                params = []

                if results_per_page is not None:
                    update_fields.append("results_per_page = ?")
                    params.append(results_per_page)

                if sort_order is not None:
                    update_fields.append("sort_order = ?")
                    params.append(sort_order)

                if default_search_type is not None:
                    update_fields.append("default_search_type = ?")
                    params.append(default_search_type)

                if update_fields:
                    # Add user_id to params
                    params.append(user_id)

                    # Execute update query
                    query = f"UPDATE search_preferences SET {', '.join(update_fields)} WHERE user_id = ?"
                    self.cur.execute(query, params)
            else:
                # Insert new preferences
                self.cur.execute("""
                INSERT INTO search_preferences (user_id, results_per_page, sort_order, default_search_type)
                VALUES (?, ?, ?, ?)
                """, (user_id,
                      results_per_page if results_per_page is not None else 5,
                      sort_order if sort_order is not None else 'desc',
                      default_search_type if default_search_type is not None else 'phone'))

            self.conn.commit()
            return True
        except Exception as e:
            print(f"Error saving search preferences: {e}")
            return False

    def get_search_preferences(self, user_id):
        """Get user's search preferences"""
        try:
            # Check if search_preferences table exists
            self.cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='search_preferences'")
            if not self.cur.fetchone():
                return {
                    'results_per_page': 5,
                    'sort_order': 'desc',
                    'default_search_type': 'phone'
                }

            # Get user's preferences
            self.cur.execute("""
            SELECT results_per_page, sort_order, default_search_type
            FROM search_preferences
            WHERE user_id = ?
            """, (user_id,))

            result = self.cur.fetchone()

            if result:
                return {
                    'results_per_page': result[0],
                    'sort_order': result[1],
                    'default_search_type': result[2]
                }
            else:
                return {
                    'results_per_page': 5,
                    'sort_order': 'desc',
                    'default_search_type': 'phone'
                }
        except Exception as e:
            print(f"Error getting search preferences: {e}")
            return {
                'results_per_page': 5,
                'sort_order': 'desc',
                'default_search_type': 'phone'
            }

    def init_tables(self):
        # Create users table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS users (
            user_id INTEGER PRIMARY KEY,
            username TEXT,
            full_name TEXT,
            phone_number TEXT,
            language TEXT DEFAULT 'tm',
            is_vip BOOLEAN DEFAULT 0,
            vip_expiry_date TIMESTAMP,
            searches_today INTEGER DEFAULT 0,
            total_searches INTEGER DEFAULT 0,
            join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            referrer_id INTEGER,
            phone_verified BOOLEAN DEFAULT 0,
            is_blocked BOOLEAN DEFAULT 0
        )
        """)

        # Check if columns exist, if not add them
        self.cur.execute("PRAGMA table_info(users)")
        columns = self.cur.fetchall()
        column_names = [column[1] for column in columns]

        # Add search_points column if it doesn't exist
        if 'search_points' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN search_points INTEGER DEFAULT 1")
                print("Added search_points column to users table")
            except Exception as e:
                print(f"Error adding search_points column: {e}")

        # Add total_searches column if it doesn't exist
        if 'total_searches' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN total_searches INTEGER DEFAULT 0")
                print("Added total_searches column to users table")
            except Exception as e:
                print(f"Error adding total_searches column: {e}")

        # Add phone_verified column if it doesn't exist
        if 'phone_verified' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN phone_verified BOOLEAN DEFAULT 0")
                print("Added phone_verified column to users table")
            except Exception as e:
                print(f"Error adding phone_verified column: {e}")

        # Add daily_gift_received column if it doesn't exist
        if 'daily_gift_received' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN daily_gift_received BOOLEAN DEFAULT 0")
                print("Added daily_gift_received column to users table")
            except Exception as e:
                print(f"Error adding daily_gift_received column: {e}")

        # Add vip_expiry_date column if it doesn't exist
        if 'vip_expiry_date' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN vip_expiry_date TIMESTAMP")
                print("Added vip_expiry_date column to users table")
            except Exception as e:
                print(f"Error adding vip_expiry_date column: {e}")

        # Add is_blocked column if it doesn't exist
        if 'is_blocked' not in column_names:
            try:
                self.cur.execute("ALTER TABLE users ADD COLUMN is_blocked BOOLEAN DEFAULT 0")
                print("Added is_blocked column to users table")
            except Exception as e:
                print(f"Error adding is_blocked column: {e}")

        # Check if promo_codes table has the new columns
        self.cur.execute("PRAGMA table_info(promo_codes)")
        promo_columns = self.cur.fetchall()
        promo_column_names = [column[1] for column in promo_columns]

        # Add expiry_date column if it doesn't exist
        if 'expiry_date' not in promo_column_names:
            try:
                self.cur.execute("ALTER TABLE promo_codes ADD COLUMN expiry_date TIMESTAMP")
                print("Added expiry_date column to promo_codes table")
            except Exception as e:
                print(f"Error adding expiry_date column: {e}")

        # Add promo_type column if it doesn't exist
        if 'promo_type' not in promo_column_names:
            try:
                self.cur.execute("ALTER TABLE promo_codes ADD COLUMN promo_type TEXT DEFAULT 'points'")
                print("Added promo_type column to promo_codes table")
            except Exception as e:
                print(f"Error adding promo_type column: {e}")

        # Add vip_days column if it doesn't exist
        if 'vip_days' not in promo_column_names:
            try:
                self.cur.execute("ALTER TABLE promo_codes ADD COLUMN vip_days INTEGER DEFAULT 0")
                print("Added vip_days column to promo_codes table")
            except Exception as e:
                print(f"Error adding vip_days column: {e}")

        # Check if notifications table exists
        self.cur.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='notifications'")
        if not self.cur.fetchone():
            try:
                # Create notifications table
                self.cur.execute("""
                CREATE TABLE IF NOT EXISTS notifications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    title TEXT,
                    message TEXT,
                    notification_type TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at TIMESTAMP,
                    created_by INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    FOREIGN KEY (created_by) REFERENCES users (user_id)
                )
                """)
                print("Created notifications table")

                # Create user_notifications table
                self.cur.execute("""
                CREATE TABLE IF NOT EXISTS user_notifications (
                    user_id INTEGER,
                    notification_id INTEGER,
                    is_read BOOLEAN DEFAULT 0,
                    read_at TIMESTAMP,
                    PRIMARY KEY (user_id, notification_id),
                    FOREIGN KEY (user_id) REFERENCES users (user_id),
                    FOREIGN KEY (notification_id) REFERENCES notifications (id)
                )
                """)
                print("Created user_notifications table")
            except Exception as e:
                print(f"Error creating notification tables: {e}")

        # Channel subscription functionality has been removed

        self.conn.commit()

        # Create searches table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS searches (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            query TEXT,
            search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """)

        # Create referrals table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS referrals (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            referrer_id INTEGER,
            referred_id INTEGER,
            join_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (referrer_id) REFERENCES users (user_id),
            FOREIGN KEY (referred_id) REFERENCES users (user_id)
        )
        """)

        # Create favorites table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS favorites (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            phone_number TEXT,
            full_name TEXT,
            address TEXT,
            passport TEXT,
            birth_info TEXT,
            sim_id TEXT,
            saved_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """)

        # Create search_history table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS search_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            query TEXT,
            search_type TEXT,
            results_count INTEGER,
            search_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id)
        )
        """)

        # Create promo_codes table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS promo_codes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            code TEXT UNIQUE,
            points INTEGER,
            max_uses INTEGER,
            current_uses INTEGER DEFAULT 0,
            created_by INTEGER,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expiry_date TIMESTAMP,
            promo_type TEXT DEFAULT 'points',
            vip_days INTEGER DEFAULT 0,
            FOREIGN KEY (created_by) REFERENCES users (user_id)
        )
        """)

        # Create notifications table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS notifications (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT,
            message TEXT,
            notification_type TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            expires_at TIMESTAMP,
            created_by INTEGER,
            is_active BOOLEAN DEFAULT 1,
            FOREIGN KEY (created_by) REFERENCES users (user_id)
        )
        """)

        # Create user_notifications table to track which users have seen which notifications
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS user_notifications (
            user_id INTEGER,
            notification_id INTEGER,
            is_read BOOLEAN DEFAULT 0,
            read_at TIMESTAMP,
            PRIMARY KEY (user_id, notification_id),
            FOREIGN KEY (user_id) REFERENCES users (user_id),
            FOREIGN KEY (notification_id) REFERENCES notifications (id)
        )
        """)

        # Create promo_code_uses table
        self.cur.execute("""
        CREATE TABLE IF NOT EXISTS promo_code_uses (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            promo_code_id INTEGER,
            used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (user_id),
            FOREIGN KEY (promo_code_id) REFERENCES promo_codes (id)
        )
        """)

        self.conn.commit()

    def get_new_users(self, days=30):
        """Get users who joined in the last X days"""
        try:
            # Check if joined_date column exists
            self.cur.execute("PRAGMA table_info(users)")
            columns = self.cur.fetchall()
            column_names = [column[1] for column in columns]

            # Add joined_date column if it doesn't exist
            if 'joined_date' not in column_names:
                try:
                    # SQLite doesn't support adding columns with non-constant defaults
                    # So we add the column without a default, then update it
                    self.cur.execute("ALTER TABLE users ADD COLUMN joined_date TIMESTAMP")
                    # Update existing rows with current timestamp
                    self.cur.execute("UPDATE users SET joined_date = CURRENT_TIMESTAMP WHERE joined_date IS NULL")
                    print("Added joined_date column to users table")
                    self.conn.commit()
                except Exception as e:
                    print(f"Error adding joined_date column: {e}")

            # Calculate date X days ago
            import datetime
            days_ago = (datetime.datetime.now() - datetime.timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')

            # Get users who joined after that date
            self.cur.execute("""
                SELECT user_id, username, full_name, phone_number,
                       COALESCE(joined_date, CURRENT_TIMESTAMP) as joined_date
                FROM users
                WHERE COALESCE(joined_date, CURRENT_TIMESTAMP) >= ?
                ORDER BY joined_date DESC
            """, (days_ago,))

            users = self.cur.fetchall()
            result = []

            for user in users:
                result.append({
                    'user_id': user[0],
                    'username': user[1],
                    'full_name': user[2],
                    'phone_number': user[3],
                    'joined_date': user[4]
                })

            return result
        except Exception as e:
            print(f"Error getting new users: {e}")
            return []

    def get_total_searches_count(self):
        """Get the total number of searches from search_history table"""
        try:
            self.cur.execute("SELECT COUNT(*) FROM search_history")
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting total searches count: {e}")
            return 0

    def get_active_users_count(self):
        """Get the number of active users in the last 30 days"""
        try:
            self.cur.execute("""
                SELECT COUNT(DISTINCT user_id) FROM search_history
                WHERE search_time >= datetime('now', '-30 days')
            """)
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting active users count: {e}")
            return 0

    def get_today_searches_count(self):
        """Get the number of searches made today"""
        try:
            today = datetime.datetime.now().strftime('%Y-%m-%d')
            self.cur.execute("""
                SELECT COUNT(*) FROM search_history
                WHERE DATE(search_time) = ?
            """, (today,))
            result = self.cur.fetchone()
            return result[0] if result else 0
        except Exception as e:
            print(f"Error getting today searches count: {e}")
            return 0
        
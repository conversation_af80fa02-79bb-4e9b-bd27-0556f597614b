# Import telegram modules with try/except to avoid IDE warnings
try:
    from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ReplyKeyboardRemove, ReplyKeyboardMarkup
    from utils.constants import ParseMode
    from telegram.ext import CallbackContext
except ImportError:
    # Define placeholder classes for type hints
    class Update: pass
    class CallbackContext: pass
    class ParseMode:
        HTML = "HTML"
    class InlineKeyboardButton: pass
    class InlineKeyboardMarkup: pass
    class ReplyKeyboardRemove: pass
    class ReplyKeyboardMarkup: pass
import re
import time
import os
from utils.db import Database
from utils.keyboards import (
    get_search_cancel_keyboard, get_new_search_keyboard, get_subscription_keyboard,
    get_search_keyboard, get_filter_keyboard, get_interactive_result_keyboard,
    get_search_verification_keyboard, get_phone_request_keyboard
)
from utils.languages import get_message
from handlers.search_suggestions import add_suggestions_to_message, handle_suggestion_callback
from utils.security import check_input_safety, sanitize_input, log_security_event

def format_result_for_export(result, language='tm'):
    """Format a search result for export to a text file"""
    phone = result.get('phone_number', '')
    name = result.get('full_name', '')
    address = result.get('address', '')
    passport = result.get('passport', '')
    birth_info = result.get('birth_info', '')
    sim_id = result.get('sim_id', '')

    # Format the result with more details based on language
    if language == 'ru':
        export_text = f"=== РЕЗУЛЬТАТ ПОИСКА ===\n\n"
        export_text += f"НОМЕР ТЕЛЕФОНА: {phone}\n"
        if name:
            export_text += f"ИМЯ: {name}\n"
        if address:
            export_text += f"АДРЕС: {address}\n"
        if passport:
            export_text += f"ПАСПОРТ: {passport}\n"
        if birth_info:
            export_text += f"ИНФОРМАЦИЯ О РОЖДЕНИИ: {birth_info}\n"
        if sim_id:
            export_text += f"SIM ID: {sim_id}\n"
    elif language == 'en':
        export_text = f"=== SEARCH RESULT ===\n\n"
        export_text += f"PHONE NUMBER: {phone}\n"
        if name:
            export_text += f"NAME: {name}\n"
        if address:
            export_text += f"ADDRESS: {address}\n"
        if passport:
            export_text += f"PASSPORT: {passport}\n"
        if birth_info:
            export_text += f"BIRTH INFO: {birth_info}\n"
        if sim_id:
            export_text += f"SIM ID: {sim_id}\n"
    else:  # Default to Turkmen
        export_text = f"=== GÖZLEG NETIJESI ===\n\n"
        export_text += f"TELEFON BELGISI: {phone}\n"
        if name:
            export_text += f"ADY: {name}\n"
        if address:
            export_text += f"SALGYSY: {address}\n"
        if passport:
            export_text += f"PASPORT: {passport}\n"
        if birth_info:
            export_text += f"DOGLAN ÝERI WE SENESI: {birth_info}\n"
        if sim_id:
            export_text += f"SIM ID: {sim_id}\n"

    # Add a timestamp
    import datetime
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    if language == 'ru':
        export_text += f"\nЭкспортировано: {now}\n"
        export_text += f"Сгенерировано ботом: @tmcell993bot\n"
    elif language == 'en':
        export_text += f"\nExported on: {now}\n"
        export_text += f"Generated by: @tmcell993bot\n"
    else:
        export_text += f"\nEksport edilen wagty: {now}\n"
        export_text += f"Bot tarapyndan döredildi: @tmcell993bot\n"

    return export_text

def format_search_result(result, language='tm', result_number=1, total_results=1):
    """Format search result for display"""
    # Clean up the data to ensure it's properly formatted
    phone = result.get('phone_number', '').strip()
    name = result.get('full_name', '').strip() or 'N/A'
    address = result.get('address', '').strip() or 'N/A'
    passport = result.get('passport', '').strip() or 'N/A'
    birth_info = result.get('birth_info', '').strip() or 'N/A'
    sim_id = result.get('sim_id', '').strip() or 'N/A'

    # Format the search result in a clean, structured way based on user's language
    # Using HTML formatting to make text selectable/copyable
    if language == 'ru':
        formatted_result = f"""✅ Поиск завершен!
🔍 Найдено {total_results} результатов

📱 Номер:
<code>{phone}</code>

👤 Имя:
<code>{name}</code>

🏠 Адрес:
<code>{address}</code>

📗 Паспорт:
<code>{passport}</code>

📅 Место и дата рождения:
<code>{birth_info}</code>

🆔 SIM ID:
<code>{sim_id}</code>

🤖 Бот: @tmcell993bot"""
    elif language == 'en':
        formatted_result = f"""✅ Search completed!
🔍 {total_results} results found

📱 Number:
<code>{phone}</code>

👤 Full Name:
<code>{name}</code>

🏠 Address:
<code>{address}</code>

📗 Passport:
<code>{passport}</code>

📅 Place and date of birth:
<code>{birth_info}</code>

🆔 SIM ID:
<code>{sim_id}</code>

🤖 Bot: @baglan_gozleg_bot"""
    else:  # Default to Turkmen
        formatted_result = f"""✅ Gözleg tamamlandy!
🔍 {total_results} sany netije tapyldy

📱 Nomeri:
<code>{phone}</code>

👤 Ady:
<code>{name}</code>

🏠 Adresi:
<code>{address}</code>

📗 Passport:
<code>{passport}</code>

📅 Doglan ýeri we senesi:
<code>{birth_info}</code>

🆔 SIM ID:
<code>{sim_id}</code>

🤖 Bot: @baglan_gozleg_bot"""

    # If there are multiple results, add a result number indicator
    if total_results > 1:
        if language == 'ru':
            formatted_result = f"📋 Результат {result_number}/{total_results}\n\n" + formatted_result
        elif language == 'en':
            formatted_result = f"📋 Result {result_number}/{total_results}\n\n" + formatted_result
        else:
            formatted_result = f"📋 Netije {result_number}/{total_results}\n\n" + formatted_result

    return formatted_result

# Global variable to store search results for interactive features
search_results_cache = {}

def search_callback(update: Update, context: CallbackContext):
    """Handle search-related callbacks"""
    query = update.callback_query
    user_id = query.from_user.id

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is blocked
    user_info = db.get_user_info(user_id)
    if user_info and user_info.get('is_blocked', False):
        # User is blocked, send blocked message
        if language == 'ru':
            blocked_message = "⛔ <b>Ваш аккаунт заблокирован.</b>\n\nВы не можете использовать бота. Если вы считаете, что это ошибка, пожалуйста, свяжитесь с администратором."
        elif language == 'en':
            blocked_message = "⛔ <b>Your account has been blocked.</b>\n\nYou cannot use the bot. If you believe this is an error, please contact an administrator."
        else:
            blocked_message = "⛔ <b>Siziň hasabyňyz blokirlendi.</b>\n\nSiz boty ulanyp bilmersiňiz. Eger bu ýalňyşlyk diýip pikir edýän bolsaňyz, administrator bilen habarlaşyň."

        query.answer("Siziň hasabyňyz blokirlendi.")
        query.edit_message_text(blocked_message, parse_mode='HTML')
        return

    # Check if phone is verified
    from handlers.phone_verification import is_phone_verified

    if query.data == "search_start":
        # Check if phone is verified
        if not is_phone_verified(user_id):
            # Phone not verified, show verification request
            query.edit_message_text(
                get_message('phone_confirm', language),
                reply_markup=get_search_verification_keyboard(language),
                parse_mode=ParseMode.HTML
            )
        else:
            # Phone verified, show search options
            query.edit_message_text(
                get_message('search_options', language),
                reply_markup=get_search_keyboard(language)
            )

    elif query.data == "request_phone_verification":
        # Import phone verification handler
        from handlers.phone_verification import request_phone_verification
        # Request phone verification
        request_phone_verification(update, context)
        query.answer("Telefon belgiňizi tassyklamak üçin düwmä basyň")

    elif query.data == "search_number":
        # Check if user has search points
        user_info = db.get_user_info(user_id)
        search_points = user_info.get('search_points', 0)
        is_vip = user_info.get('is_vip', False)

        if search_points > 0 or is_vip:
            # User has search points or is VIP
            query.edit_message_text(
                get_message('search_form', language).format(searches_left=search_points),
                reply_markup=get_search_cancel_keyboard(language)
            )
            context.user_data['awaiting_phone_number'] = True
            context.user_data['search_type'] = 'phone'
        else:
            # User has no search points
            query.edit_message_text(
                get_message('no_points', language),
                reply_markup=get_subscription_keyboard(language)
            )

    elif query.data == "search_name":
        # Check if user has search points
        user_info = db.get_user_info(user_id)
        search_points = user_info.get('search_points', 0)
        is_vip = user_info.get('is_vip', False)

        if search_points > 0 or is_vip:
            # User has search points or is VIP
            query.edit_message_text(
                get_message('search_name_form', language).format(searches_left=search_points),
                reply_markup=get_search_cancel_keyboard(language)
            )
            context.user_data['awaiting_phone_number'] = True
            context.user_data['search_type'] = 'name'
        else:
            # User has no search points
            query.edit_message_text(
                get_message('no_points', language),
                reply_markup=get_subscription_keyboard(language)
            )

    elif query.data == "search_passport":
        # Check if user has search points
        user_info = db.get_user_info(user_id)
        search_points = user_info.get('search_points', 0)
        is_vip = user_info.get('is_vip', False)

        if search_points > 0 or is_vip:
            # User has search points or is VIP
            query.edit_message_text(
                get_message('search_passport_form', language).format(searches_left=search_points),
                reply_markup=get_search_cancel_keyboard(language)
            )
            context.user_data['awaiting_phone_number'] = True
            context.user_data['search_type'] = 'passport'
        else:
            # User has no search points
            query.edit_message_text(
                get_message('no_points', language),
                reply_markup=get_subscription_keyboard(language)
            )

    elif query.data == "combined_search":
        # Check if user has search points
        user_info = db.get_user_info(user_id)
        search_points = user_info.get('search_points', 0)
        is_vip = user_info.get('is_vip', False)

        if search_points > 0 or is_vip:
            # User has search points or is VIP
            query.edit_message_text(
                get_message('combined_search_prompt', language).format(searches_left=search_points),
                reply_markup=get_search_cancel_keyboard(language)
            )
            context.user_data['awaiting_phone_number'] = True
            context.user_data['search_type'] = 'combined'
        else:
            # User has no search points
            query.edit_message_text(
                get_message('no_points', language),
                reply_markup=get_subscription_keyboard(language)
            )

    elif query.data == "search_cancel":
        # Cancel search and return to main menu
        from utils.keyboards import get_main_menu_keyboard
        query.edit_message_text(
            get_message('welcome', language),
            reply_markup=get_main_menu_keyboard(language, user_id)
        )
        if 'awaiting_phone_number' in context.user_data:
            del context.user_data['awaiting_phone_number']
        if 'search_type' in context.user_data:
            del context.user_data['search_type']

    # Filter functionality
    elif query.data == "filter_results":
        # Show filter options
        query.edit_message_text(
            get_message('filter_title', language),
            reply_markup=get_filter_keyboard(language)
        )

    elif query.data == "filter_city":
        # Ask for city to filter by
        query.edit_message_text(
            get_message('city_filter_prompt', language),
            reply_markup=get_search_cancel_keyboard(language)
        )
        context.user_data['awaiting_filter'] = 'city'

    elif query.data == "filter_year":
        # Ask for birth year to filter by
        query.edit_message_text(
            get_message('year_filter_prompt', language),
            reply_markup=get_search_cancel_keyboard(language)
        )
        context.user_data['awaiting_filter'] = 'year'

    elif query.data == "filter_name":
        # Ask for name to filter by
        query.edit_message_text(
            get_message('name_filter_prompt', language),
            reply_markup=get_search_cancel_keyboard(language)
        )
        context.user_data['awaiting_filter'] = 'name'

    elif query.data == "filter_reset":
        # Reset all filters
        if 'filters' in context.user_data:
            del context.user_data['filters']
        query.answer("Filtrler üstünlikli arassalandy")
        # Return to search menu
        query.edit_message_text(
            get_message('search_options', language),
            reply_markup=get_search_keyboard(language)
        )

    # Handle copy text callback
    elif query.data.startswith("copy_text_"):
        result_id = int(query.data.split("_")[-1])
        if user_id in search_results_cache and result_id < len(search_results_cache[user_id]):
            result = search_results_cache[user_id][result_id]
            # Create a plain text version for copying
            share_text = format_search_result(result, language)
            plain_text = share_text.replace('<code>', '').replace('</code>', '')

            # Send as a text message with special formatting to make it easier to copy
            # Use monospace font to make it look like a pre-formatted text block
            context.bot.send_message(
                chat_id=user_id,
                text=f"<pre>{plain_text}</pre>",
                parse_mode=ParseMode.HTML
            )

            # Send instructions on how to copy
            context.bot.send_message(
                chat_id=user_id,
                text="📋 Maglumaty kopyalamak üçin ýokarky habaryň üstüne basyň we 'Kopyala' düwmesini saýlaň."
            )

            query.answer("Tekst kopyalamak üçin taýýn!")

    # Handle any legacy detailed view requests
    elif query.data.startswith("view_details_"):
        # Inform user that this feature is no longer available
        if language == 'ru':
            query.answer("Эта функция больше не доступна.")
        elif language == 'en':
            query.answer("This feature is no longer available.")
        else:
            query.answer("Bu funksiýa indi elýeterli däl.")
        return

    elif query.data.startswith("share_result_"):
        result_id = int(query.data.split("_")[-1])
        if user_id in search_results_cache and result_id < len(search_results_cache[user_id]):
            result = search_results_cache[user_id][result_id]
            try:
                # Create a shareable message
                share_text = format_search_result(result, language)

                # Create a plain text version for sharing outside Telegram
                plain_text = share_text.replace('<code>', '').replace('</code>', '')

                # Send as a new message that can be forwarded
                context.bot.send_message(
                    chat_id=user_id,
                    text=share_text,
                    parse_mode=ParseMode.HTML
                )

                # Create a keyboard with enhanced share options
                share_keyboard = [
                    [InlineKeyboardButton("💬 Telegramda paýlaş", switch_inline_query=plain_text[:20])],
                    [InlineKeyboardButton("📲 Kopyala", callback_data=f"copy_text_{result_id}")],
                    [InlineKeyboardButton("📱 WhatsApp-da paýlaş", url=f"https://wa.me/?text={plain_text[:100].replace(' ', '%20')}")],
                    [InlineKeyboardButton("📧 Email bilen paýlaş", url=f"mailto:?subject=Gözleg%20Netijesi&body={plain_text[:200].replace(' ', '%20')}")],
                    [InlineKeyboardButton("📤 Paýlaşmak", callback_data=f"share_result_{result['phone_number']}")],
                ]

                # Send a follow-up message with share options
                context.bot.send_message(
                    chat_id=user_id,
                    text="💬 Paýlaşmak üçin aşakdaky düwmeleri ulaň:",
                    reply_markup=InlineKeyboardMarkup(share_keyboard)
                )

                query.answer("Netije paýlaşmak üçin taýýn!")
            except Exception as e:
                print(f"Error sharing result: {e}")
                query.answer("Paýlaşmakda ýalňyşlyk ýüze çykdy.")

    elif query.data.startswith("save_result_"):
        result_id = int(query.data.split("_")[-1])
        if user_id in search_results_cache and result_id < len(search_results_cache[user_id]):
            result = search_results_cache[user_id][result_id]
            # Save to database
            db = Database()
            success = db.add_to_favorites(user_id, result)
            if success:
                query.answer(get_message('favorite_added', language))
            else:
                query.answer(get_message('favorite_exists', language))

    elif query.data.startswith("export_result_"):
        # Handle export result action
        try:
            result_id = int(query.data.split("_")[-1])
            if user_id in search_results_cache and result_id < len(search_results_cache[user_id]):
                result = search_results_cache[user_id][result_id]

                # Format the result for export
                export_text = format_result_for_export(result, language)

                # Create a temporary file
                import tempfile
                import os

                # Create a temporary file with .txt extension
                fd, path = tempfile.mkstemp(suffix='.txt')
                try:
                    with os.fdopen(fd, 'w', encoding='utf-8') as tmp:
                        tmp.write(export_text)

                    # Send the file
                    with open(path, 'rb') as file:
                        # Send as a new message instead of using query.bot
                        context.bot.send_document(
                            chat_id=user_id,
                            document=file,
                            filename=f"search_result_{result.get('phone_number', 'unknown')}.txt",
                            caption=get_message('export_caption', language)
                        )

                    # Inform user
                    if language == 'ru':
                        query.answer("Результат экспортирован в текстовый файл.")
                    elif language == 'en':
                        query.answer("Result exported as a text file.")
                    else:
                        query.answer("Netije tekst faýl görnüşinde eksport edildi.")

                    # Also send a plain text version for easy copying
                    context.bot.send_message(
                        chat_id=user_id,
                        text=export_text
                    )
                finally:
                    # Clean up the temp file
                    try:
                        os.unlink(path)
                    except Exception as e:
                        print(f"Error removing temp file: {e}")
            else:
                if language == 'ru':
                    query.answer("Результат не найден.")
                elif language == 'en':
                    query.answer("Result not found.")
                else:
                    query.answer("Netije tapylmady.")
        except Exception as e:
            print(f"Error exporting result: {e}")
            if language == 'ru':
                query.answer("Ошибка при экспорте результата.")
            elif language == 'en':
                query.answer("Error exporting result.")
            else:
                query.answer("Netijäni eksport etmekde ýalňyşlyk ýüze çykdy.")

    # Channel subscription functionality has been removed

def handle_search_query(update: Update, context: CallbackContext):
    """Handle text messages for search queries"""
    user_id = update.effective_user.id
    search_query = update.message.text.strip()

    # Security check - sanitize input
    search_query = sanitize_input(search_query)

    # Check for potentially harmful content
    is_safe, reason = check_input_safety(search_query)
    if not is_safe:
        # Log security event
        log_security_event(user_id, 'unsafe_input', {
            'input': search_query,
            'reason': reason,
            'timestamp': time.time()
        })

        # Get user's language
        db = Database()
        language = db.get_user_language(user_id)

        # Send warning message
        if language == 'ru':
            warning_message = "⚠️ <b>Обнаружен потенциально опасный ввод.</b>\n\nПожалуйста, используйте только буквы, цифры и обычные символы."
        elif language == 'en':
            warning_message = "⚠️ <b>Potentially harmful input detected.</b>\n\nPlease use only letters, numbers, and regular symbols."
        else:
            warning_message = "⚠️ <b>Potensial howply giriş anyklandy.</b>\n\nDiňe harplar, sanlar we adaty nyşanlar ulanyň."

        update.message.reply_text(warning_message, parse_mode='HTML')
        return

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Check if user is blocked
    user_info = db.get_user_info(user_id)
    if user_info and user_info.get('is_blocked', False):
        # User is blocked, send blocked message
        if language == 'ru':
            blocked_message = "⛔ <b>Ваш аккаунт заблокирован.</b>\n\nВы не можете использовать бота. Если вы считаете, что это ошибка, пожалуйста, свяжитесь с администратором."
        elif language == 'en':
            blocked_message = "⛔ <b>Your account has been blocked.</b>\n\nYou cannot use the bot. If you believe this is an error, please contact an administrator."
        else:
            blocked_message = "⛔ <b>Siziň hasabyňyz blokirlendi.</b>\n\nSiz boty ulanyp bilmersiňiz. Eger bu ýalňyşlyk diýip pikir edýän bolsaňyz, administrator bilen habarlaşyň."

        update.message.reply_text(blocked_message, parse_mode='HTML')

        # Log security event
        log_security_event(user_id, 'blocked_user_attempt', {
            'action': 'search',
            'query': search_query,
            'timestamp': time.time()
        })
        return

    # Apply rate limiting
    current_time = time.time()
    if 'last_search_time' in context.user_data:
        time_diff = current_time - context.user_data['last_search_time']
        if time_diff < 1:  # Less than 1 second between searches
            if language == 'ru':
                message = "⚠️ <b>Слишком быстрые запросы!</b>\n\nПожалуйста, подождите немного между поисками."
            elif language == 'en':
                message = "⚠️ <b>Too fast requests!</b>\n\nPlease wait a moment between searches."
            else:
                message = "⚠️ <b>Gaty çalt haýyşlar!</b>\n\nGözlegler arasynda biraz garaşyň."

            update.message.reply_text(message, parse_mode='HTML')

            # Log rate limit event
            log_security_event(user_id, 'rate_limit', {
                'time_diff': time_diff,
                'timestamp': time.time()
            })
            return

    # Update last search time
    context.user_data['last_search_time'] = current_time

    # Check if we're expecting phone verification
    if context.user_data.get('awaiting_phone_verification', False):
        # Handle phone verification via contact message
        from handlers.phone_verification import handle_contact_message
        if update.message.contact:
            # Process contact message
            if handle_contact_message(update, context):
                return
        else:
            # Not a contact message, remind user to use the button
            update.message.reply_text(
                get_message('phone_verification_failed', language),
                reply_markup=get_phone_request_keyboard(language),
                parse_mode=ParseMode.HTML
            )
            return

    # Check if we're expecting a filter input
    if context.user_data.get('awaiting_filter'):
        filter_type = context.user_data.get('awaiting_filter')
        # Clear the flag
        del context.user_data['awaiting_filter']

        # Initialize filters if not exists
        if 'filters' not in context.user_data:
            context.user_data['filters'] = {}

        # Add the filter
        context.user_data['filters'][filter_type] = search_query

        # Confirm filter added
        update.message.reply_text(
            f"Filter applied: {filter_type} = {search_query}",
            reply_markup=get_filter_keyboard(language)
        )
        return

    # Check if we're expecting a search query
    if context.user_data.get('awaiting_phone_number'):
        # Get the search type
        search_type = context.user_data.get('search_type', 'phone')

        # Clear the flags
        context.user_data['awaiting_phone_number'] = False
        if 'search_type' in context.user_data:
            del context.user_data['search_type']

        # Validate query based on search type
        if search_type == 'phone':
            # Check if the query starts with '+993' - we should convert it to 993
            if search_query.startswith("+993"):
                # Convert +993 to 993 for searching
                search_query = search_query.replace("+993", "993")
                # Continue with the search

            # Check if the query starts with '993' or '63' - these are valid
            is_valid_start = search_query.startswith("993") or search_query.startswith("63")
            if not is_valid_start and not search_query.replace('+', '').replace(' ', '').replace('-', '').isdigit():
                update.message.reply_text(
                    get_message('no_results', language),
                    reply_markup=get_new_search_keyboard(language)
                )
                return
        elif search_type == 'name':
            # Check if name query is valid (at least 3 characters)
            if len(search_query) < 3:
                update.message.reply_text(
                    get_message('invalid_name_query', language),
                    reply_markup=get_new_search_keyboard(language)
                )
                return
        elif search_type == 'passport':
            # Check if passport query is valid (at least 4 characters)
            if len(search_query) < 4:
                update.message.reply_text(
                    get_message('invalid_passport_query', language),
                    reply_markup=get_new_search_keyboard(language)
                )
                return

        # Check if phone is verified - MANDATORY
        from handlers.phone_verification import is_phone_verified
        if not is_phone_verified(user_id):
            # Phone is not verified, request verification with strong message
            from handlers.phone_verification import request_phone_verification

            # Send a clear message about mandatory verification
            if language == 'ru':
                message = "⚠️ <b>Обязательная верификация</b>\n\nДля использования функции поиска необходимо подтвердить свой номер телефона. Без верификации вы не сможете выполнять поиск."
            elif language == 'en':
                message = "⚠️ <b>Mandatory verification</b>\n\nYou must verify your phone number to use the search function. Without verification, you cannot perform searches."
            else:  # Default to Turkmen
                message = "⚠️ <b>Hökmany tassyklama</b>\n\nGözleg funksiýasyny ulanmak üçin telefon belgiňizi tassyklamagyňyz zerurdyr. Tassyklamasyz gözleg geçirip bilmersiňiz."

            update.message.reply_text(
                message,
                parse_mode='HTML'
            )

            request_phone_verification(update, context)
            return

        # Check if user has search points
        user_info = db.get_user_info(user_id)
        search_points = user_info.get('search_points', 0)
        is_vip = user_info.get('is_vip', False)

        if search_points <= 0 and not is_vip:
            # User has no search points
            update.message.reply_text(
                get_message('no_points', language),
                reply_markup=get_subscription_keyboard(language)
            )
            return

        # Notify user that we're searching with a simple text message
        try:
            searching_message = update.message.reply_text(
                get_message('searching', language).format(phone_number=search_query),
                parse_mode=ParseMode.HTML
            )
        except Exception as e:
            print(f"Error sending search message: {e}")
            # Try one more time with a simpler message
            try:
                searching_message = update.message.reply_text(
                    f"🔍 Gözlenýär: {search_query}...",
                    parse_mode=ParseMode.HTML
                )
            except Exception as e2:
                print(f"Error sending simple search message: {e2}")
                # Create a dummy message object that we can reference later
                class DummyMessage:
                    def edit_text(self, *args, **kwargs):
                        pass
                searching_message = DummyMessage()

        # Search based on the type
        if search_type == 'combined':
            # For combined search, try to identify what kind of data was provided
            # and search in multiple ways
            combined_results = []

            # Check if it looks like a phone number
            if search_query.replace('+', '').replace(' ', '').replace('-', '').isdigit():
                # If the query starts with '+993', convert it to '993' for searching
                if search_query.startswith("+993"):
                    search_query = search_query.replace("+993", "993")

                phone_results = db.search_phone_number(search_query, 'phone')
                combined_results.extend(phone_results)

            # Check if it's long enough to be a name
            if len(search_query) >= 3:
                name_results = db.search_phone_number(search_query, 'name')
                # Add only new results (avoid duplicates)
                for result in name_results:
                    if not any(r.get('phone_number') == result.get('phone_number') for r in combined_results):
                        combined_results.append(result)

            # Check if it's long enough to be a passport
            if len(search_query) >= 4:
                passport_results = db.search_phone_number(search_query, 'passport')
                # Add only new results (avoid duplicates)
                for result in passport_results:
                    if not any(r.get('phone_number') == result.get('phone_number') for r in combined_results):
                        combined_results.append(result)

            results = combined_results
        else:
            # Regular search
            results = db.search_phone_number(search_query, search_type)

        # Apply filters if any
        if 'filters' in context.user_data and results:
            filtered_results = []
            filters = context.user_data['filters']
            print(f"Applying filters: {filters}")

            for result in results:
                include_result = True

                # Check city filter
                if 'city' in filters and filters['city'].strip():
                    city_filter = filters['city'].lower().strip()
                    address = result.get('address', '').lower()
                    if city_filter not in address:
                        include_result = False
                        print(f"City filter '{city_filter}' not found in '{address}'")

                # Check year filter
                if include_result and 'year' in filters and filters['year'].strip():
                    year_filter = filters['year'].strip()
                    birth_info = result.get('birth_info', '')
                    # Try to find year in birth_info
                    year_match = re.search(r'\b(19\d{2}|20\d{2})\b', birth_info)
                    if not year_match or year_filter not in year_match.group(0):
                        include_result = False
                        print(f"Year filter '{year_filter}' not found in '{birth_info}'")

                # Check name filter
                if include_result and 'name' in filters and filters['name'].strip():
                    name_filter = filters['name'].lower().strip()
                    full_name = result.get('full_name', '').lower()
                    if name_filter not in full_name:
                        include_result = False
                        print(f"Name filter '{name_filter}' not found in '{full_name}'")

                # If passed all filters, add to filtered results
                if include_result:
                    filtered_results.append(result)

            # Replace original results with filtered ones
            print(f"Filtered results: {len(filtered_results)} out of {len(results)}")
            results = filtered_results

        # Cache the results for interactive features
        search_results_cache[user_id] = results

        # Record the search with more details
        db.record_search(user_id, search_query, search_type, len(results))

        # Save search data to file
        from utils.search_storage import save_search_data
        save_search_data(user_id, search_query, results)

        try:
            # Delete the searching message
            searching_message.delete()
        except Exception:
            # If we can't delete the message, just continue
            pass

        if results:
            # Send the results
            total_results = len(results)
            for i, result in enumerate(results[:3], 1):  # Limit to 3 results
                # Add interactive buttons to each result
                update.message.reply_text(
                    format_search_result(result, language, i, total_results),
                    parse_mode=ParseMode.HTML,
                    reply_markup=get_interactive_result_keyboard(language, i-1)
                )

                # For the last result, add the new search keyboard
                if i == min(3, total_results):
                    update.message.reply_text(
                        get_message('search_again', language),
                        reply_markup=get_new_search_keyboard(language)
                    )
        else:
            # No results found
            # Add suggestions to the message
            message_text = get_message('no_results', language)
            message_text, suggestions_keyboard = add_suggestions_to_message(message_text, user_id, search_query, language)

            if suggestions_keyboard:
                update.message.reply_text(
                    message_text,
                    parse_mode=ParseMode.HTML,
                    reply_markup=suggestions_keyboard
                )
            else:
                update.message.reply_text(
                    message_text,
                    reply_markup=get_new_search_keyboard(language)
                )
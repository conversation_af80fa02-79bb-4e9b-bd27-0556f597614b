from telegram import Bo<PERSON>
from utils.db import Database
from config import TOKEN
import logging

# Enable logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s', level=logging.INFO
)
logger = logging.getLogger(__name__)

def give_daily_point(user_id, bot, language='tm'):
    """Give a daily free point to a user and notify them"""
    db = Database()

    # Check if user already received daily gift
    user_info = db.get_user_info(user_id)
    if user_info.get('daily_gift_received', False):
        logger.info(f"User {user_id} already received daily gift today")
        return False

    # Add 1 point to the user and mark as gift
    db.add_search_points(user_id, 1, mark_as_gift=True)

    # Send notification message
    try:
        message = "🎁 Günlik mugt gözleg balyňyz berildi! @tmcell993bot"
        if language == 'ru':
            message = "🎁 Вам начислен ежедневный бесплатный балл поиска! @tmcell993bot"
        elif language == 'en':
            message = "🎁 Your daily free search point has been credited! @tmcell993bot"

        bot.send_message(chat_id=user_id, text=message)
        logger.info(f"Daily point given to user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error sending daily point notification to user {user_id}: {e}")
        return False

def check_and_give_daily_points():
    """Check all non-VIP users and give them a daily point if they have 0 points"""
    db = Database()
    bot = Bot(TOKEN)

    # Get all users
    users = db.get_all_users_with_points()

    # Count of users who received points
    points_given = 0

    for user in users:
        user_id = user['user_id']
        is_vip = user['is_vip']
        points = user['search_points']
        language = user['language']

        # Skip VIP users
        if is_vip:
            continue

        # Give point to users with 0 points
        if points == 0:
            if give_daily_point(user_id, bot, language):
                points_given += 1

    logger.info(f"Daily points check completed. {points_given} users received points.")
    return points_given

def check_points_after_search(user_id, bot):
    """Check if user has 0 points after a search and give them a point if needed"""
    db = Database()

    # Get user info
    user_info = db.get_user_info(user_id)

    # Skip VIP users
    if user_info.get('is_vip', False):
        return False

    # Check if user already received daily gift
    if user_info.get('daily_gift_received', False):
        logger.info(f"User {user_id} already received daily gift today, not giving another point")
        return False

    # Check if user has 0 points
    if user_info.get('search_points', 0) == 0:
        # Give them a point
        language = user_info.get('language', 'tm')
        return give_daily_point(user_id, bot, language)

    return False

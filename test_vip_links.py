#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify VIP payment links are updated correctly
"""

import re
import os

def check_file_for_old_links(filepath):
    """Check a file for old admin/owner links"""
    old_patterns = [
        r'nerwa_degme',
        r'baglan_support', 
        r'baglan_group',
        r'Eziz_Official',
        r'a_nazarow77',
        r'Baglan Gözleg'
    ]
    
    new_patterns = [
        r'TMCELLadmin',
        r'Arslan_Vpns',
        r'rnxGG',
        r'TM CELL'
    ]
    
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        old_found = []
        new_found = []
        
        for pattern in old_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                old_found.append(pattern)
        
        for pattern in new_patterns:
            if re.search(pattern, content, re.IGNORECASE):
                new_found.append(pattern)
        
        return old_found, new_found
        
    except Exception as e:
        return [], []

def main():
    """Main test function"""
    print("🚀 TM CELL Bot - VIP Links Verification")
    print("=" * 50)
    
    # Files to check
    important_files = [
        'handlers/vip.py',
        'utils/keyboards.py',
        'utils/languages.py',
        'config.py'
    ]
    
    all_clean = True
    
    for filepath in important_files:
        if os.path.exists(filepath):
            print(f"\n📄 Checking {filepath}...")
            old_found, new_found = check_file_for_old_links(filepath)
            
            if old_found:
                print(f"❌ Old links found: {old_found}")
                all_clean = False
            else:
                print("✅ No old links found")
            
            if new_found:
                print(f"✅ New links found: {new_found}")
            else:
                print("⚠️ No new links found")
        else:
            print(f"❌ File not found: {filepath}")
            all_clean = False
    
    print("\n" + "=" * 50)
    print("📊 VIP LINKS VERIFICATION:")
    
    if all_clean:
        print("🎉 ALL VIP LINKS UPDATED SUCCESSFULLY!")
        print("✅ No old admin/owner links found")
        print("✅ VIP payment system ready with new contacts")
        print("\n📋 New VIP contacts:")
        print("   • Owner: @TMCELLadmin")
        print("   • Admin 1: @Arslan_Vpns") 
        print("   • Admin 2: @rnxGG")
        print("   • Support: @TMCELLadmin")
        print("   • Account: TM CELL")
        return True
    else:
        print("❌ SOME OLD LINKS STILL EXIST!")
        print("⚠️ Please check the files above")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)

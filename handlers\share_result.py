from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import CallbackContext
from utils.db import Database
from utils.phone_data import get_user_data_by_phone
import qrcode
import io
import os
import json
import datetime

def share_result_callback(update: Update, context: CallbackContext):
    """Handle sharing search results with QR code"""
    query = update.callback_query
    user_id = query.from_user.id
    callback_data = query.data

    # Extract phone number from callback data (assume callback_data = 'share_result_<phone_number>')
    phone_number = callback_data.split('_', 2)[-1]

    # Get user's language
    db = Database()
    language = db.get_user_language(user_id)

    # Try to get result from context first
    result = None
    if 'search_results' in context.user_data:
        for r in context.user_data['search_results']:
            if r.get('phone_number', '') == phone_number:
                result = r
                break
    # If not found, try to get from big file
    if not result:
        result = get_user_data_by_phone(phone_number)
        if result:
            # Adapt keys if needed
            if 'phone' in result and 'phone_number' not in result:
                result['phone_number'] = result['phone']

    if not result:
        if language == 'ru':
            query.answer("Результат поиска не найден.")
        elif language == 'en':
            query.answer("Search result not found.")
        else:
            query.answer("Gözleg netijesi tapylmady.")
        return

    # Create directory for QR codes if it doesn't exist
    if not os.path.exists('qr_codes'):
        os.makedirs('qr_codes')

    # Generate filename with timestamp
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"qr_codes/result_{user_id}_{timestamp}.png"

    try:
        # Create data for QR code
        result_data = {
            'phone_number': result.get('phone_number', ''),
            'full_name': result.get('full_name', ''),
            'address': result.get('address', ''),
            'passport': result.get('passport', ''),
            'birth_info': result.get('birth_info', ''),
            'sim_id': result.get('sim_id', ''),
            'generated_by': f"@tmcell993bot",
            'timestamp': timestamp
        }
        result_json = json.dumps(result_data, ensure_ascii=False)
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(result_json)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")
        img.save(filename)
        with open(filename, 'rb') as photo:
            if language == 'ru':
                caption = f"📱 <b>Результат поиска - QR код</b>\n\nИмя: {result.get('full_name', '')}\nТелефон: {result.get('phone_number', '')}\n\nОтсканируйте QR-код, чтобы получить полную информацию."
            elif language == 'en':
                caption = f"📱 <b>Search Result - QR Code</b>\n\nName: {result.get('full_name', '')}\nPhone: {result.get('phone_number', '')}\n\nScan the QR code to get the full information."
            else:
                caption = f"📱 <b>Gözleg netijesi - QR kod</b>\n\nAdy: {result.get('full_name', '')}\nTelefon: {result.get('phone_number', '')}\n\nDoly maglumaty almak üçin QR kody skanirläň."
            query.message.reply_photo(
                photo=photo,
                caption=caption,
                parse_mode='HTML'
            )
        if language == 'ru':
            query.answer("QR-код успешно создан!")
        elif language == 'en':
            query.answer("QR code successfully created!")
        else:
            query.answer("QR kod üstünlikli döredildi!")
    except Exception as e:
        print(f"Error creating QR code: {e}")
        if language == 'ru':
            query.answer("Ошибка при создании QR-кода.")
        elif language == 'en':
            query.answer("Error creating QR code.")
        else:
            query.answer("QR kod döretmekde ýalňyşlyk.")
    try:
        if os.path.exists(filename):
            os.remove(filename)
    except:
        pass